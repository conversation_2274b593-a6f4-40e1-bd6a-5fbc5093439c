# Deployment Guide

This guide provides comprehensive instructions for deploying the Contemporary Art Store Vue.js application to various static hosting platforms.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Build Configuration](#build-configuration)
- [Environment Setup](#environment-setup)
- [Deployment Platforms](#deployment-platforms)
  - [Netlify](#netlify)
  - [Vercel](#vercel)
  - [GitHub Pages](#github-pages)
  - [Other Static Hosts](#other-static-hosts)
- [Environment Variables](#environment-variables)
- [Troubleshooting](#troubleshooting)
- [Performance Optimization](#performance-optimization)

## Prerequisites

Before deploying, ensure you have:

1. **Node.js** (version 18 or higher)
2. **npm** or **yarn** package manager
3. **Git** for version control
4. **NOWPayments API key** (for payment functionality)
5. A hosting platform account (Netlify, Vercel, etc.)

## Build Configuration

The application is optimized for production with the following features:

- **Code Splitting**: All routes are lazy-loaded for optimal performance
- **Bundle Optimization**: Vendor libraries are split into separate chunks
- **Asset Optimization**: Images and CSS are optimized and cached
- **Modern Build Target**: Uses ESNext for modern browsers
- **Minification**: JavaScript and CSS are minified using Terser

### Build Commands

```bash
# Install dependencies
npm install

# Build for production
npm run build

# Preview production build locally
npm run preview
```

## Environment Setup

### 1. Create Environment File

Copy the production environment template:

```bash
cp .env.production .env.local
```

### 2. Configure Environment Variables

Edit `.env.local` with your production values:

```env
NODE_ENV=production
VITE_APP_NAME=Contemporary Art Store
VITE_APP_URL=https://your-domain.com
VITE_NOWPAYMENTS_API_KEY=your_production_api_key
VITE_NOWPAYMENTS_SANDBOX=false
VITE_ADMIN_EMAIL=<EMAIL>
```

## Deployment Platforms

### Netlify

Netlify offers seamless deployment with automatic builds and CDN distribution.

#### Method 1: Git Integration (Recommended)

1. **Connect Repository**:
   - Log in to [Netlify](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub/GitLab repository

2. **Configure Build Settings**:
   - Build command: `npm run build`
   - Publish directory: `dist`
   - Node version: `18`

3. **Environment Variables**:
   - Go to Site settings → Environment variables
   - Add your production environment variables

4. **Deploy**:
   - Netlify will automatically build and deploy on every push to main branch

#### Method 2: Manual Deploy

```bash
# Build the application
npm run build

# Install Netlify CLI
npm install -g netlify-cli

# Deploy to Netlify
netlify deploy --prod --dir=dist
```

#### Configuration Files

The repository includes:
- `netlify.toml`: Build and redirect configuration
- `public/_redirects`: SPA routing rules

### Vercel

Vercel provides excellent performance with edge functions and global CDN.

#### Method 1: Git Integration (Recommended)

1. **Connect Repository**:
   - Log in to [Vercel](https://vercel.com)
   - Click "New Project"
   - Import your Git repository

2. **Configure Project**:
   - Framework Preset: Vite
   - Build Command: `npm run build`
   - Output Directory: `dist`

3. **Environment Variables**:
   - Add environment variables in project settings

4. **Deploy**:
   - Vercel automatically deploys on Git pushes

#### Method 2: Vercel CLI

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel --prod
```

#### Configuration

The `vercel.json` file is included for custom configuration.

### GitHub Pages

Deploy directly from your GitHub repository using GitHub Actions.

#### Setup Instructions

1. **Enable GitHub Pages**:
   - Go to repository Settings → Pages
   - Source: GitHub Actions

2. **Add Workflow File**:
   - Copy `github-pages-deploy.yml` to `.github/workflows/`
   - Commit and push to trigger deployment

3. **Configure Environment Variables**:
   - Go to repository Settings → Secrets and variables → Actions
   - Add your environment variables as repository secrets

4. **Custom Domain** (Optional):
   - Add `CNAME` file to `public/` directory with your domain

#### Base URL Configuration

For GitHub Pages, update `vite.config.js` if using a custom repository name:

```javascript
base: process.env.NODE_ENV === 'production' ? '/your-repo-name/' : '/',
```

### Other Static Hosts

The application can be deployed to any static hosting service:

#### General Steps

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Upload `dist/` folder** to your hosting provider

3. **Configure SPA routing**:
   - Ensure all routes serve `index.html`
   - Configure 404 fallback to `index.html`

#### Popular Alternatives

- **Cloudflare Pages**: Similar to Netlify/Vercel
- **Firebase Hosting**: Google's hosting solution
- **AWS S3 + CloudFront**: Enterprise-grade hosting
- **DigitalOcean App Platform**: Simple deployment platform

## Environment Variables

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `VITE_NOWPAYMENTS_API_KEY` | NOWPayments API key | `your_api_key_here` |
| `VITE_APP_URL` | Production URL | `https://your-domain.com` |

### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `VITE_NOWPAYMENTS_SANDBOX` | Enable sandbox mode | `false` |
| `VITE_ENABLE_REGISTRATION` | Enable user registration | `true` |
| `VITE_ENABLE_GUEST_CHECKOUT` | Enable guest checkout | `true` |
| `VITE_ADMIN_EMAIL` | Admin contact email | `<EMAIL>` |

### Security Notes

- Never commit `.env.local` or production environment files
- Use platform-specific environment variable management
- Rotate API keys regularly
- Use different API keys for staging and production

## Troubleshooting

### Common Issues

#### 1. 404 Errors on Page Refresh

**Problem**: Direct URLs return 404 errors
**Solution**: Configure SPA routing redirects

For Netlify:
```
# _redirects file
/*    /index.html   200
```

For Vercel:
```json
{
  "rewrites": [{ "source": "/(.*)", "destination": "/index.html" }]
}
```

#### 2. Environment Variables Not Working

**Problem**: Environment variables are undefined
**Solutions**:
- Ensure variables start with `VITE_`
- Check platform-specific environment variable configuration
- Verify build process includes environment variables

#### 3. Payment Integration Issues

**Problem**: NOWPayments not working
**Solutions**:
- Verify API key is correct for production/sandbox
- Check `VITE_NOWPAYMENTS_SANDBOX` setting
- Ensure CORS is configured for your domain

#### 4. Large Bundle Size

**Problem**: Bundle size warnings
**Solutions**:
- Check `vite.config.js` chunk splitting configuration
- Analyze bundle with `npm run build -- --analyze`
- Consider lazy loading more components

### Build Errors

#### Node.js Version Issues

Ensure Node.js version 18 or higher:
```bash
node --version
npm --version
```

#### Memory Issues

For large builds, increase Node.js memory:
```bash
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

## Performance Optimization

### Build Analysis

Analyze your bundle size:
```bash
# Build with analysis
npm run build

# The build process will show chunk sizes and warnings
```

### Optimization Checklist

- [ ] All routes use lazy loading
- [ ] Images are optimized and properly sized
- [ ] CSS is split and minified
- [ ] JavaScript is minified and tree-shaken
- [ ] Static assets have proper caching headers
- [ ] CDN is configured for global distribution

### Monitoring

Consider adding performance monitoring:

1. **Google Analytics**: Track user interactions
2. **Sentry**: Error tracking and performance monitoring
3. **Lighthouse**: Regular performance audits

## Security Considerations

### Production Security

- Enable HTTPS (most platforms do this automatically)
- Configure Content Security Policy headers
- Set proper CORS policies
- Use environment variables for sensitive data
- Regular security updates for dependencies

### Headers Configuration

Security headers are configured in:
- `netlify.toml` for Netlify
- `vercel.json` for Vercel
- Server configuration for other platforms

## Support

For deployment issues:

1. Check platform-specific documentation
2. Review build logs for errors
3. Test locally with `npm run preview`
4. Verify environment variable configuration
5. Check network requests in browser developer tools

## Additional Resources

- [Vite Deployment Guide](https://vitejs.dev/guide/static-deploy.html)
- [Vue.js Deployment Guide](https://vuejs.org/guide/best-practices/production-deployment.html)
- [NOWPayments Documentation](https://documenter.getpostman.com/view/7907941/S1a32n38)
- [Netlify Documentation](https://docs.netlify.com/)
- [Vercel Documentation](https://vercel.com/docs)
- [GitHub Pages Documentation](https://docs.github.com/en/pages)