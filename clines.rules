# Cline Rules for Vue.js + Vite + Neon Database Project

## Project Overview
This is a Vue.js 3 application built with Vite, using Neon PostgreSQL database with their API for data operations.

## Technology Stack
- **Frontend**: Vue.js 3 with Composition API
- **Build Tool**: Vite
- **Database**: Neon PostgreSQL (serverless)
- **Styling**: [Specify: Tailwind CSS / CSS Modules / Sass / etc.]
- **State Management**: [Specify: Pinia / Vuex / Composables]
- **HTTP Client**: [Specify: Axios / Fetch API]
- **Type Safety**: TypeScript (if applicable)

## Project Structure
```
src/
├── components/          # Vue components
├── composables/         # Vue composition functions
├── views/              # Page components
├── router/             # Vue Router configuration
├── stores/             # State management (Pinia/Vuex)
├── services/           # API service layer
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
└── assets/             # Static assets
```

## Coding Standards

### Vue.js Guidelines
- Use Vue 3 Composition API with `<script setup>` syntax
- Prefer composables over mixins
- Use TypeScript for type safety (if TypeScript is enabled)
- Follow Vue.js official style guide
- Use PascalCase for component names
- Use kebab-case for component file names
- Destructure reactive properties properly with `toRefs()` when needed

### Component Structure
```vue
<template>
  <!-- Template content -->
</template>

<script setup lang="ts">
// Imports
// Props & emits
// Reactive state
// Computed properties
// Methods
// Lifecycle hooks
// Watchers
</script>

<style scoped>
/* Component-specific styles */
</style>
```

### Database Integration (Neon)
- Use Neon's serverless driver for database connections
- Implement connection pooling appropriately
- Store database connection string in environment variables
- Use parameterized queries to prevent SQL injection
- Implement proper error handling for database operations
- Use transactions for multi-step operations

### Environment Configuration
Required environment variables:
```
VITE_NEON_DATABASE_URL=postgresql://[user]:[password]@[host]/[database]
VITE_API_BASE_URL=your-api-base-url
VITE_APP_ENV=development|production
```

## File and Folder Conventions

### Naming Conventions
- **Components**: PascalCase (e.g., `UserProfile.vue`)
- **Files**: kebab-case (e.g., `user-profile.vue`)
- **Directories**: kebab-case
- **Variables/Functions**: camelCase
- **Constants**: UPPER_SNAKE_CASE
- **Database tables**: snake_case
- **API endpoints**: kebab-case

### Import/Export Patterns
- Use named exports for utilities and composables
- Use default exports for Vue components
- Group imports: external libraries first, then internal modules
- Use absolute imports with path aliases configured in Vite

## Database Guidelines

### Schema Design
- Use snake_case for table and column names
- Include created_at and updated_at timestamps
- Use UUIDs for primary keys when appropriate
- Implement proper foreign key constraints
- Add appropriate indexes for query performance

### Query Patterns
- Use prepared statements
- Implement connection pooling
- Handle database errors gracefully
- Use migrations for schema changes
- Implement database seeders for development data

### Example Database Service Pattern
```javascript
// services/database.js
import { neon } from '@neondatabase/serverless';

const sql = neon(import.meta.env.VITE_NEON_DATABASE_URL);

export class DatabaseService {
  static async getUsers() {
    try {
      const result = await sql`SELECT * FROM users ORDER BY created_at DESC`;
      return result;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch users');
    }
  }
}
```

## API Design Patterns

### Service Layer Architecture
- Create dedicated service classes for each entity
- Implement consistent error handling
- Use TypeScript interfaces for API responses
- Implement request/response interceptors
- Add loading states and error states to composables

### Composables Pattern
```javascript
// composables/useUsers.js
import { ref, reactive } from 'vue'
import { DatabaseService } from '@/services/database'

export function useUsers() {
  const users = ref([])
  const loading = ref(false)
  const error = ref(null)

  const fetchUsers = async () => {
    loading.value = true
    error.value = null
    
    try {
      users.value = await DatabaseService.getUsers()
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  return {
    users: readonly(users),
    loading: readonly(loading),
    error: readonly(error),
    fetchUsers
  }
}
```

## State Management
- Use Pinia for complex state management
- Keep local component state when data doesn't need to be shared
- Use composables for reusable stateful logic
- Implement proper state normalization for complex data

## Security Guidelines
- Validate all inputs on both client and server side
- Use environment variables for sensitive configuration
- Implement proper authentication and authorization
- Sanitize data before database operations
- Use HTTPS in production
- Implement rate limiting for API endpoints

## Performance Guidelines
- Implement lazy loading for routes
- Use `v-memo` for expensive list renderings
- Optimize database queries with proper indexing
- Implement pagination for large datasets
- Use Vite's code splitting features
- Optimize images and assets
- Implement caching strategies where appropriate

## Error Handling
- Implement global error handling
- Use try-catch blocks for async operations
- Provide user-friendly error messages
- Log errors appropriately for debugging
- Implement fallback UI states
- Handle network errors gracefully

## Testing Guidelines
- Write unit tests for utilities and composables
- Write component tests for Vue components
- Write integration tests for API services
- Use Vitest as the testing framework
- Mock database calls in tests
- Aim for meaningful test coverage

## Development Workflow
- Use conventional commits for commit messages
- Create feature branches for new development
- Implement proper code review process
- Use ESLint and Prettier for code formatting
- Run tests before committing
- Use Husky for git hooks

## Code Review Checklist
- [ ] Code follows Vue.js best practices
- [ ] Database queries are optimized and secure
- [ ] Error handling is implemented
- [ ] TypeScript types are properly defined
- [ ] Tests are written and passing
- [ ] No console.log statements in production code
- [ ] Environment variables are used for configuration
- [ ] Code is properly documented

## Deployment Guidelines
- Build optimized production bundle
- Set appropriate environment variables
- Configure Neon database for production
- Implement proper monitoring and logging
- Use CDN for static assets
- Configure proper caching headers

## Documentation Requirements
- Document all public APIs
- Maintain README with setup instructions
- Document database schema changes
- Keep environment variable documentation updated
- Document deployment procedures

## Troubleshooting Common Issues
- Database connection timeouts: Check connection pooling
- Build errors: Verify Vite configuration and dependencies
- Runtime errors: Check browser console and error boundaries
- Performance issues: Use Vue DevTools for debugging
- Database query issues: Check Neon dashboard for query logs

## Additional Notes
- Always test database connections in development
- Use Neon's branching feature for database migrations
- Monitor database usage and performance metrics
- Keep dependencies updated regularly
- Follow Vue.js community best practices and updates