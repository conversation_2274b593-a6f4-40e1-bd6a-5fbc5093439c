import express from 'express';
import cors from 'cors';
import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config(); // Load environment variables from .env file

const app = express();
const port = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  connectionTimeoutMillis: 30000, // 30 seconds
  idleTimeoutMillis: 30000,
  max: 20,
  ssl: process.env.DATABASE_URL?.includes('neon.tech') ? { rejectUnauthorized: false } : false
});

// Database connection state
let isDbConnected = false;

// Test database connection
const connectWithRetry = async () => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    client.release();
    console.log('Database connected:', result.rows[0].now);
    isDbConnected = true;
  } catch (err) {
    console.error('Database connection failed:', err.message);
    isDbConnected = false;
    console.log('Retrying database connection in 10 seconds...');
    setTimeout(connectWithRetry, 10000); // Retry after 10 seconds
  }
};

// Initialize database connection
connectWithRetry();

// Sample data fallback
const getSampleArtworks = () => [
  {
    id: 1,
    title: 'Sunset Over the Lake',
    description: 'A beautiful painting capturing the serene sunset over a calm lake.',
    price: 1500.00,
    image_url: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=500',
    artist_id: 1,
    artist: 'Sample Artist',
    category: 'Landscape',
    style: 'Impressionism',
    medium: 'Oil on canvas',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    title: 'Abstract Cityscape',
    description: 'Modern abstract art depicting a vibrant city at night.',
    price: 2200.50,
    image_url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500',
    artist_id: 1,
    artist: 'Sample Artist',
    category: 'Abstract',
    style: 'Cubism',
    medium: 'Acrylic',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 3,
    title: 'Portrait of a Lady',
    description: 'A classic portrait with intricate details and rich colors.',
    price: 1800.00,
    image_url: 'https://images.unsplash.com/photo-1578321272176-b7bbc0679853?w=500',
    artist_id: 1,
    artist: 'Sample Artist',
    category: 'Portrait',
    style: 'Realism',
    medium: 'Charcoal',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Get all artworks
app.get('/api/artworks', async (req, res) => {
  try {
    // Check if database is connected
    if (!isDbConnected) {
      console.log('Database not connected, returning sample data');
      return res.json(getSampleArtworks());
    }

    const result = await pool.query(`
      SELECT
        a.*,
        u.username as artist
      FROM artworks a
      LEFT JOIN users u ON a.artist_id = u.id
      ORDER BY a.created_at DESC
    `);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching artworks:', err);
    // Return sample data as fallback
    res.json(getSampleArtworks());
  }
});

// Get artwork by ID
app.get('/api/artworks/:id', async (req, res) => {
  const { id } = req.params;
  try {
    // Check if database is connected
    if (!isDbConnected) {
      const sampleArtworks = getSampleArtworks();
      const artwork = sampleArtworks.find(a => a.id === parseInt(id));
      if (!artwork) {
        return res.status(404).json({ error: 'Artwork not found' });
      }
      return res.json(artwork);
    }

    const result = await pool.query(`
      SELECT
        a.*,
        u.username as artist
      FROM artworks a
      LEFT JOIN users u ON a.artist_id = u.id
      WHERE a.id = $1
    `, [id]);
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Artwork not found' });
    }
    res.json(result.rows[0]);
  } catch (err) {
    console.error(`Error fetching artwork with id ${id}:`, err);
    // Fallback to sample data
    const sampleArtworks = getSampleArtworks();
    const artwork = sampleArtworks.find(a => a.id === parseInt(id));
    if (!artwork) {
      return res.status(404).json({ error: 'Artwork not found' });
    }
    res.json(artwork);
  }
});

// Create artwork
app.post('/api/artworks', async (req, res) => {
  try {
    const { title, description, price, image_url, artist_id, category, style, medium } = req.body;
    const result = await pool.query(
      'INSERT INTO artworks (title, description, price, image_url, artist_id, category, style, medium) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *',
      [title, description, price, image_url, artist_id, category, style, medium]
    );
    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error creating artwork:', err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// Update artwork
app.put('/api/artworks/:id', async (req, res) => {
  const { id } = req.params;
  try {
    const { title, description, price, image_url, artist_id, category, style, medium } = req.body;
    const result = await pool.query(
      'UPDATE artworks SET title = $1, description = $2, price = $3, image_url = $4, artist_id = $5, category = $6, style = $7, medium = $8, updated_at = CURRENT_TIMESTAMP WHERE id = $9 RETURNING *',
      [title, description, price, image_url, artist_id, category, style, medium, id]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Artwork not found' });
    }
    res.json(result.rows[0]);
  } catch (err) {
    console.error(`Error updating artwork with id ${id}:`, err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// Delete artwork
app.delete('/api/artworks/:id', async (req, res) => {
  const { id } = req.params;
  try {
    const result = await pool.query('DELETE FROM artworks WHERE id = $1 RETURNING id', [id]);
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Artwork not found' });
    }
    res.status(204).send(); // No content
  } catch (err) {
    console.error(`Error deleting artwork with id ${id}:`, err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// Search artworks
app.get('/api/artworks/search', async (req, res) => {
  const { query } = req.query;
  if (!query) {
    return res.status(400).json({ error: 'Search query is required' });
  }
  try {
    // Check if database is connected
    if (!isDbConnected) {
      const sampleArtworks = getSampleArtworks();
      const filteredArtworks = sampleArtworks.filter(artwork =>
        artwork.title.toLowerCase().includes(query.toLowerCase()) ||
        artwork.description.toLowerCase().includes(query.toLowerCase()) ||
        artwork.artist.toLowerCase().includes(query.toLowerCase()) ||
        artwork.category.toLowerCase().includes(query.toLowerCase()) ||
        artwork.style.toLowerCase().includes(query.toLowerCase()) ||
        artwork.medium.toLowerCase().includes(query.toLowerCase())
      );
      return res.json(filteredArtworks);
    }

    const lowercaseQuery = `%${query.toLowerCase()}%`;
    const result = await pool.query(
      `SELECT
        a.*,
        u.username as artist
      FROM artworks a
      LEFT JOIN users u ON a.artist_id = u.id
      WHERE LOWER(a.title) LIKE $1
         OR LOWER(a.description) LIKE $1
         OR LOWER(u.username) LIKE $1
         OR LOWER(a.category) LIKE $1
         OR LOWER(a.style) LIKE $1
         OR LOWER(a.medium) LIKE $1
      ORDER BY a.created_at DESC`,
      [lowercaseQuery]
    );
    res.json(result.rows);
  } catch (err) {
    console.error(`Error searching artworks with query "${query}":`, err);
    // Fallback to sample data search
    const sampleArtworks = getSampleArtworks();
    const filteredArtworks = sampleArtworks.filter(artwork =>
      artwork.title.toLowerCase().includes(query.toLowerCase()) ||
      artwork.description.toLowerCase().includes(query.toLowerCase()) ||
      artwork.artist.toLowerCase().includes(query.toLowerCase()) ||
      artwork.category.toLowerCase().includes(query.toLowerCase()) ||
      artwork.style.toLowerCase().includes(query.toLowerCase()) ||
      artwork.medium.toLowerCase().includes(query.toLowerCase())
    );
    res.json(filteredArtworks);
  }
});

// Basic route for the root path
app.get('/', (req, res) => {
  res.json({
    message: 'Contemporary Art Store API Server',
    status: 'running',
    database: isDbConnected ? 'connected' : 'disconnected',
    timestamp: new Date().toISOString()
  });
});

app.listen(port, () => {
  console.log(`Server running on http://localhost:${port}`);
});
