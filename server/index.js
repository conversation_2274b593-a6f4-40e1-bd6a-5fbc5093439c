import express from 'express';
import cors from 'cors';
import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config(); // Load environment variables from .env file

const app = express();
const port = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  connectionTimeoutMillis: 10000, // 10 seconds
});

// Test database connection
const connectWithRetry = () => {
  pool.connect((err, client, release) => {
    if (err) {
      console.error('Error acquiring client, retrying in 5 seconds...', err.stack);
      setTimeout(connectWithRetry, 5000); // Retry after 5 seconds
      return;
    }
    client.query('SELECT NOW()', (err, result) => {
      release();
      if (err) {
        console.error('Error executing query, retrying in 5 seconds...', err.stack);
        setTimeout(connectWithRetry, 5000); // Retry after 5 seconds
        return;
      }
      console.log('Database connected:', result.rows[0].now);
    });
  });
};

connectWithRetry();

// Get all artworks
app.get('/api/artworks', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT 
        a.*, 
        u.username as artist 
      FROM artworks a
      JOIN users u ON a.artist_id = u.id
      ORDER BY a.created_at DESC
    `);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching artworks:', err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// Get artwork by ID
app.get('/api/artworks/:id', async (req, res) => {
  const { id } = req.params;
  try {
    const result = await pool.query(`
      SELECT 
        a.*, 
        u.username as artist 
      FROM artworks a
      JOIN users u ON a.artist_id = u.id
      WHERE a.id = $1
    `, [id]);
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Artwork not found' });
    }
    res.json(result.rows[0]);
  } catch (err) {
    console.error(`Error fetching artwork with id ${id}:`, err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// Create artwork
app.post('/api/artworks', async (req, res) => {
  try {
    const { title, description, price, image_url, artist_id, category, style, medium } = req.body;
    const result = await pool.query(
      'INSERT INTO artworks (title, description, price, image_url, artist_id, category, style, medium) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *',
      [title, description, price, image_url, artist_id, category, style, medium]
    );
    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error creating artwork:', err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// Update artwork
app.put('/api/artworks/:id', async (req, res) => {
  const { id } = req.params;
  try {
    const { title, description, price, image_url, artist_id, category, style, medium } = req.body;
    const result = await pool.query(
      'UPDATE artworks SET title = $1, description = $2, price = $3, image_url = $4, artist_id = $5, category = $6, style = $7, medium = $8, updated_at = CURRENT_TIMESTAMP WHERE id = $9 RETURNING *',
      [title, description, price, image_url, artist_id, category, style, medium, id]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Artwork not found' });
    }
    res.json(result.rows[0]);
  } catch (err) {
    console.error(`Error updating artwork with id ${id}:`, err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// Delete artwork
app.delete('/api/artworks/:id', async (req, res) => {
  const { id } = req.params;
  try {
    const result = await pool.query('DELETE FROM artworks WHERE id = $1 RETURNING id', [id]);
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Artwork not found' });
    }
    res.status(204).send(); // No content
  } catch (err) {
    console.error(`Error deleting artwork with id ${id}:`, err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// Search artworks
app.get('/api/artworks/search', async (req, res) => {
  const { query } = req.query;
  if (!query) {
    return res.status(400).json({ error: 'Search query is required' });
  }
  try {
    const lowercaseQuery = `%${query.toLowerCase()}%`;
    const result = await pool.query(
      `SELECT 
        a.*, 
        u.username as artist 
      FROM artworks a
      JOIN users u ON a.artist_id = u.id
      WHERE LOWER(a.title) LIKE $1 
         OR LOWER(a.description) LIKE $1 
         OR LOWER(u.username) LIKE $1 
         OR LOWER(a.category) LIKE $1 
         OR LOWER(a.style) LIKE $1 
         OR LOWER(a.medium) LIKE $1
      ORDER BY a.created_at DESC`,
      [lowercaseQuery]
    );
    res.json(result.rows);
  } catch (err) {
    console.error(`Error searching artworks with query "${query}":`, err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
});

// Basic route for the root path
app.get('/', (req, res) => {
  res.send('Server is running!');
});

app.listen(port, () => {
  console.log(`Server running on http://localhost:${port}`);
});
