# ==================================================
# NEON DATABASE CONFIGURATION
# ==================================================

# Primary connection string (recommended method)
# Get this from your Neon dashboard under "Connection Details"
VITE_NEON_DATABASE_URL="postgresql://username:<EMAIL>/neondb?sslmode=require"

# Alternative: Individual connection parameters
# VITE_NEON_HOST="ep-example-123456.us-east-2.aws.neon.tech"
# VITE_NEON_DATABASE="neondb"
# VITE_NEON_USERNAME="username" 
# VITE_NEON_PASSWORD="password"
# VITE_NEON_PORT="5432"
# VITE_NEON_SSL="true"

# ==================================================
# APPLICATION CONFIGURATION  
# ==================================================

# Environment
VITE_APP_ENV="development"

# API Configuration
VITE_API_BASE_URL="http://localhost:3000"
VITE_API_TIMEOUT="10000"

# App Settings
VITE_APP_NAME="Vue.JS, Vite Store"
VITE_APP_VERSION="1.0.0"

# ==================================================
# DEVELOPMENT SETTINGS
# ==================================================

# Enable debugging
VITE_DEBUG="true"

# Development database (if different from production)
#VITE_DEV_DATABASE_URL="postgresql://dev_user:dev_pass@localhost:5432/dev_db"

# ==================================================
# PRODUCTION SETTINGS (for .env.production)
# ==================================================

# VITE_APP_ENV="production"
# VITE_DEBUG="false"
# VITE_NEON_DATABASE_URL="your-production-connection-string"

# ==================================================
# SECURITY NOTES
# ==================================================

# 1. Never commit this file to version control
# 2. Add .env* to your .gitignore file
# 3. Use different credentials for development/production
# 4. Rotate passwords regularly
# 5. Use Neon's database branching for development

# ==================================================
# GETTING YOUR NEON CONNECTION STRING
# ==================================================

# 1. Go to https://console.neon.tech/
# 2. Select your project
# 3. Go to "Dashboard" 
# 4. Click "Connection Details"
# 5. Copy the connection string
# 6. Replace the placeholder values above

# Example connection string format:
# postgresql://[username]:[password]@[endpoint]/[database]?sslmode=require

# ==================================================
# GITIGNORE ENTRIES
# ==================================================

# Add these lines to your .gitignore file:
# .env
# .env.local
# .env.development.local
# .env.test.local
# .env.production.local