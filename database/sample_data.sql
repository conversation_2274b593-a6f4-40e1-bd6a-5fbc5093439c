-- Sample data for users table
INSERT INTO users (username, email, password_hash) VALUES
('john_doe', '<EMAIL>', 'hashed_password_1'),
('jane_smith', '<EMAIL>', 'hashed_password_2'),
('artist_one', '<EMAIL>', 'hashed_password_3'),
('user_four', '<EMAIL>', 'hashed_password_4'),
('user_five', '<EMAIL>', 'hashed_password_5')
ON CONFLICT (username) DO NOTHING;

-- Sample data for artworks table
INSERT INTO artworks (title, description, price, image_url, artist_id, category, style, medium) VALUES
('Sunset Over the Lake', 'A beautiful painting capturing the serene sunset over a calm lake.', 1500.00, 'https://example.com/images/sunset.jpg', 3, 'Landscape', 'Impressionism', 'Oil on canvas'),
('Abstract Cityscape', 'Modern abstract art depicting a vibrant city at night.', 2200.50, 'https://example.com/images/cityscape.jpg', 3, 'Abstract', 'Cubism', 'Acrylic'),
('Portrait of a Lady', 'A classic portrait with intricate details and rich colors.', 1800.00, 'https://example.com/images/portrait.jpg', 3, 'Portrait', 'Realism', 'Charcoal'),
('Digital Dreamscape', 'A futuristic landscape created with digital brushes.', 2500.00, 'https://example.com/images/dreamscape.jpg', 3, 'Digital Art', 'Surrealism', 'Digital Painting'),
('Ocean Serenity', 'Calm ocean waves under a clear sky.', 1200.00, 'https://example.com/images/ocean.jpg', 3, 'Seascape', 'Minimalism', 'Watercolor');

-- Sample data for orders table
INSERT INTO orders (user_id, total_amount, status) VALUES
(1, 1500.00, 'completed'),
(2, 2200.50, 'pending');

-- Sample data for order_items table
INSERT INTO order_items (order_id, artwork_id, quantity, price) VALUES
(1, 1, 1, 1500.00),
(2, 2, 1, 2200.50);

-- Sample data for cart_items table
INSERT INTO cart_items (user_id, artwork_id, quantity) VALUES
(1, 2, 1),
(2, 1, 1)
ON CONFLICT (user_id, artwork_id) DO NOTHING;

-- Sample data for newsletter_subscribers table
INSERT INTO newsletter_subscribers (email) VALUES
('<EMAIL>'),
('<EMAIL>'),
('<EMAIL>')
ON CONFLICT (email) DO NOTHING;
