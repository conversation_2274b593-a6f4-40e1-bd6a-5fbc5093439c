[build]
  # Build command
  command = "npm run build"
  # Directory to publish
  publish = "dist"
  # Node.js version
  environment = { NODE_VERSION = "18" }

[build.environment]
  # Production environment variables
  NODE_ENV = "production"

# SPA routing configuration
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://api.nowpayments.io;"

# Cache static assets
[[headers]]
  for = "/js/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/css/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Cache HTML with shorter duration
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=3600"

# Environment-specific redirects for development
[context.deploy-preview]
  environment = { NODE_ENV = "development" }

[context.branch-deploy]
  environment = { NODE_ENV = "development" }