{"name": "contemporary-art-store", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "node server/index.js", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "keywords": [], "author": "", "license": "ISC", "description": "Contemporary Art Store - Vue.js application with admin dashboard", "dependencies": {"@nowpaymentsio/nowpayments-api-js": "^1.0.5", "@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "pg": "^8.16.1", "pinia": "^3.0.3", "postcss": "^8.5.6", "qrcode-vue3": "^1.7.1", "tailwindcss": "^3.4.17", "vite": "^6.3.5", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.0.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitejs/plugin-vue-jsx": "^4.2.0", "eslint": "^9.29.0", "eslint-plugin-vue": "^10.2.0", "prettier": "^3.5.3", "typescript": "^5.8.3"}}