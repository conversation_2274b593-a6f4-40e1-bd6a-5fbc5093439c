import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/Home.vue')
    },
    {
      path: '/gallery',
      name: 'gallery',
      component: () => import('../views/Gallery.vue')
    },
    {
      path: '/artwork/:id',
      name: 'product-detail',
      component: () => import('../views/ProductDetail.vue'),
      props: true
    },
    {
      path: '/cart',
      name: 'cart',
      component: () => import('../views/Cart.vue')
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/Admin.vue'),
      meta: { 
        requiresAuth: true,
        requiresRole: ['admin', 'curator']
      }
    },
    {
      path: '/admin/dashboard',
      name: 'admin-dashboard',
      component: () => import('../views/Admin.vue'),
      meta: { 
        requiresAuth: true,
        requiresRole: ['admin', 'curator']
      }
    },
    {
      path: '/admin/artworks',
      name: 'admin-artworks',
      component: () => import('../views/Admin.vue'),
      meta: { 
        requiresAuth: true,
        requiresRole: ['admin', 'curator']
      }
    },
    {
      path: '/admin/orders',
      name: 'admin-orders',
      component: () => import('../views/Admin.vue'),
      meta: { 
        requiresAuth: true,
        requiresRole: ['admin', 'curator']
      }
    },
    {
      path: '/admin/users',
      name: 'admin-users',
      component: () => import('../views/Admin.vue'),
      meta: { 
        requiresAuth: true,
        requiresRole: ['admin']
      }
    },
    {
      path: '/admin/analytics',
      name: 'admin-analytics',
      component: () => import('../views/Admin.vue'),
      meta: { 
        requiresAuth: true,
        requiresRole: ['admin', 'curator']
      }
    },
    {
      path: '/admin/settings',
      name: 'admin-settings',
      component: () => import('../views/Admin.vue'),
      meta: { 
        requiresAuth: true,
        requiresRole: ['admin']
      }
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/Login.vue'),
      meta: { 
        requiresGuest: true // Redirect authenticated users away from login
      }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/Register.vue'),
      meta: { 
        requiresGuest: true // Redirect authenticated users away from register
      }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/Profile.vue'),
      meta: { 
        requiresAuth: true 
      }
    },
    {
      path: '/orders',
      name: 'orders',
      component: () => import('../views/Orders.vue'),
      meta: { 
        requiresAuth: true,
        requiresRole: ['customer']
      }
    },
    {
      path: '/order-confirmation/:orderId',
      name: 'order-confirmation',
      component: () => import('../views/OrderConfirmationView.vue'),
      props: true
    },
    // Catch-all route for 404 pages
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFound.vue')
    }
  ]
})

// Enhanced navigation guard with role-based access control
router.beforeEach(async (to, from, next) => {
  // Initialize auth store
  const authStore = useAuthStore()
  
  // Check if route requires authentication
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!authStore.isAuthenticated) {
      // Store the intended destination for redirect after login
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // Check role-based access
    const requiredRoles = to.meta.requiresRole
    if (requiredRoles && !authStore.hasRole(requiredRoles)) {
      // User doesn't have required role, redirect based on their role
      if (authStore.isAdmin || authStore.isCurator) {
        next('/admin')
      } else {
        next('/')
      }
      return
    }
  }
  
  // Check if route requires guest (unauthenticated) access
  if (to.matched.some(record => record.meta.requiresGuest)) {
    if (authStore.isAuthenticated) {
      // Redirect authenticated users based on their role
      if (authStore.isAdmin || authStore.isCurator) {
        next('/admin')
      } else {
        next('/')
      }
      return
    }
  }
  
  // Handle redirect after login
  if (to.path === '/login' && to.query.redirect && authStore.isAuthenticated) {
    next(to.query.redirect)
    return
  }
  
  next()
})

// Global after hook for route change handling
router.afterEach((to, from) => {
  // Update document title based on route
  const baseTitle = 'Contemporary Art Store'
  const routeTitles = {
    'home': 'Home',
    'gallery': 'Gallery',
    'product-detail': 'Artwork Details',
    'cart': 'Shopping Cart',
    'admin': 'Admin Dashboard',
    'admin-dashboard': 'Admin Dashboard',
    'admin-artworks': 'Artwork Management',
    'admin-orders': 'Order Management',
    'admin-users': 'User Management',
    'admin-analytics': 'Analytics & Reports',
    'admin-settings': 'System Settings',
    'login': 'Sign In',
    'register': 'Create Account',
    'profile': 'Profile',
    'orders': 'My Orders',
    'order-confirmation': 'Order Confirmation',
    'not-found': 'Page Not Found'
  }
  
  const pageTitle = routeTitles[to.name] || 'Page'
  document.title = `${pageTitle} | ${baseTitle}`
})

export default router