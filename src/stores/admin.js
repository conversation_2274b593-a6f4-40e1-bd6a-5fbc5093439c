import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { mockUsers, mockOrders } from '../services/mockData'

export const useAdminStore = defineStore('admin', () => {
  // State
  const users = ref([])
  const orders = ref([])
  const loading = ref(false)
  const error = ref(null)
  const analytics = ref({
    totalRevenue: 0,
    totalOrders: 0,
    totalUsers: 0,
    recentOrders: [],
    topArtworks: [],
    salesByMonth: []
  })

  // Computed getters
  const pendingOrders = computed(() => 
    orders.value.filter(order => order.status === 'pending')
  )
  
  const completedOrders = computed(() => 
    orders.value.filter(order => order.status === 'completed')
  )
  
  const activeUsers = computed(() => 
    users.value.filter(user => user.status !== 'suspended')
  )

  const revenueByStatus = computed(() => {
    const revenue = {
      pending: 0,
      processing: 0,
      shipped: 0,
      delivered: 0,
      completed: 0,
      cancelled: 0
    }
    
    orders.value.forEach(order => {
      if (revenue.hasOwnProperty(order.status)) {
        revenue[order.status] += order.totalAmount
      }
    })
    
    return revenue
  })

  // User Management Actions
  const fetchUsers = async () => {
    loading.value = true
    error.value = null
    try {
      // Simulate API call with mock data
      await new Promise(resolve => setTimeout(resolve, 500))
      users.value = mockUsers.map(user => ({
        ...user,
        status: user.status || 'active',
        lastLogin: user.lastLogin || new Date().toISOString(),
        orderCount: mockOrders.filter(order => order.userId === user.id).length
      }))
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const updateUserStatus = async (userId, status) => {
    loading.value = true
    error.value = null
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      const userIndex = users.value.findIndex(user => user.id === userId)
      if (userIndex !== -1) {
        users.value[userIndex].status = status
        users.value[userIndex].updatedAt = new Date().toISOString()
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateUserRole = async (userId, role) => {
    loading.value = true
    error.value = null
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      const userIndex = users.value.findIndex(user => user.id === userId)
      if (userIndex !== -1) {
        users.value[userIndex].role = role
        users.value[userIndex].updatedAt = new Date().toISOString()
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  // Order Management Actions
  const fetchOrders = async () => {
    loading.value = true
    error.value = null
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      // Enhance mock orders with additional data
      orders.value = mockOrders.map(order => ({
        ...order,
        customerName: users.value.find(user => user.id === order.userId)?.name || 'Unknown Customer',
        customerEmail: users.value.find(user => user.id === order.userId)?.email || '<EMAIL>',
        shippingAddress: {
          street: '123 Art Street',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA'
        },
        trackingNumber: order.status === 'shipped' ? `TRK${order.id}${Date.now().toString().slice(-6)}` : null
      }))
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const updateOrderStatus = async (orderId, status) => {
    loading.value = true
    error.value = null
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      const orderIndex = orders.value.findIndex(order => order.id === orderId)
      if (orderIndex !== -1) {
        orders.value[orderIndex].status = status
        orders.value[orderIndex].updatedAt = new Date().toISOString()
        
        // Add tracking number for shipped orders
        if (status === 'shipped' && !orders.value[orderIndex].trackingNumber) {
          orders.value[orderIndex].trackingNumber = `TRK${orderId}${Date.now().toString().slice(-6)}`
        }
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const getOrderById = (orderId) => {
    return orders.value.find(order => order.id === orderId)
  }

  // Analytics Actions
  const fetchAnalytics = async () => {
    loading.value = true
    error.value = null
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Calculate analytics from existing data
      const totalRevenue = orders.value
        .filter(order => order.status === 'completed')
        .reduce((sum, order) => sum + order.totalAmount, 0)
      
      const recentOrders = [...orders.value]
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5)
      
      // Mock sales by month data
      const salesByMonth = [
        { month: 'Jan', sales: 12500 },
        { month: 'Feb', sales: 15200 },
        { month: 'Mar', sales: 18700 },
        { month: 'Apr', sales: 16800 },
        { month: 'May', sales: 21300 },
        { month: 'Jun', sales: 19500 }
      ]

      analytics.value = {
        totalRevenue,
        totalOrders: orders.value.length,
        totalUsers: users.value.length,
        recentOrders,
        topArtworks: [], // Will be populated from artwork store
        salesByMonth
      }
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  // System Settings Actions
  const systemSettings = ref({
    siteName: 'Contemporary Art Store',
    currency: 'USD',
    taxRate: 0.08,
    shippingRate: 50,
    maintenanceMode: false,
    allowRegistration: true,
    emailNotifications: true
  })

  const updateSystemSettings = async (settings) => {
    loading.value = true
    error.value = null
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      systemSettings.value = { ...systemSettings.value, ...settings }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  // Category Management
  const categories = ref([
    'Pop Art',
    'Abstract Art',
    'Sculpture',
    'Painting',
    'Mixed Media',
    'Installation Art',
    'Street Art',
    'Contemporary Realism'
  ])

  const addCategory = async (categoryName) => {
    loading.value = true
    error.value = null
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      if (!categories.value.includes(categoryName)) {
        categories.value.push(categoryName)
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const removeCategory = async (categoryName) => {
    loading.value = true
    error.value = null
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      categories.value = categories.value.filter(cat => cat !== categoryName)
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  // Artist Management
  const artists = ref([
    'Andy Warhol',
    'Jackson Pollock',
    'Louise Bourgeois',
    'David Hockney',
    'Cecily Brown',
    'Kara Walker',
    'Felix Gonzalez-Torres',
    'Njideka Akunyili Crosby',
    'Banksy',
    'Willem de Kooning'
  ])

  const addArtist = async (artistName) => {
    loading.value = true
    error.value = null
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      if (!artists.value.includes(artistName)) {
        artists.value.push(artistName)
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const removeArtist = async (artistName) => {
    loading.value = true
    error.value = null
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      artists.value = artists.value.filter(artist => artist !== artistName)
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    users,
    orders,
    loading,
    error,
    analytics,
    systemSettings,
    categories,
    artists,
    
    // Computed
    pendingOrders,
    completedOrders,
    activeUsers,
    revenueByStatus,
    
    // User Management Actions
    fetchUsers,
    updateUserStatus,
    updateUserRole,
    
    // Order Management Actions
    fetchOrders,
    updateOrderStatus,
    getOrderById,
    
    // Analytics Actions
    fetchAnalytics,
    
    // System Settings Actions
    updateSystemSettings,
    
    // Category Management Actions
    addCategory,
    removeCategory,
    
    // Artist Management Actions
    addArtist,
    removeArtist
  }
})