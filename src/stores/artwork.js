import { defineStore } from 'pinia'
import { ref } from 'vue'
import { artworkService } from '../services/artworkService'

export const useArtworkStore = defineStore('artwork', () => {
  const artworks = ref([])
  const filteredArtworks = ref([])
  const loading = ref(false)
  const error = ref(null)
  const currentFilters = ref({
    search: '',
    categories: [],
    artists: [],
    mediums: [],
    styles: [],
    priceRange: { min: null, max: null }
  })
  const currentSort = ref({ field: 'createdAt', direction: 'desc' })

  const fetchArtworks = async () => {
    loading.value = true
    error.value = null
    try {
      artworks.value = await artworkService.getAll()
      applyFiltersAndSort()
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const applyFiltersAndSort = () => {
    let filtered = [...artworks.value]

    // Apply search filter
    if (currentFilters.value.search) {
      const searchTerm = currentFilters.value.search.toLowerCase()
      filtered = filtered.filter(artwork => 
        artwork.title.toLowerCase().includes(searchTerm) ||
        artwork.artist.toLowerCase().includes(searchTerm) ||
        artwork.description.toLowerCase().includes(searchTerm) ||
        artwork.category.toLowerCase().includes(searchTerm) ||
        artwork.style.toLowerCase().includes(searchTerm) ||
        artwork.medium.toLowerCase().includes(searchTerm)
      )
    }

    // Apply category filter
    if (currentFilters.value.categories.length > 0) {
      filtered = filtered.filter(artwork => 
        currentFilters.value.categories.includes(artwork.category)
      )
    }

    // Apply artist filter
    if (currentFilters.value.artists.length > 0) {
      filtered = filtered.filter(artwork => 
        currentFilters.value.artists.includes(artwork.artist)
      )
    }

    // Apply medium filter
    if (currentFilters.value.mediums.length > 0) {
      filtered = filtered.filter(artwork => 
        currentFilters.value.mediums.includes(artwork.medium)
      )
    }

    // Apply style filter
    if (currentFilters.value.styles.length > 0) {
      filtered = filtered.filter(artwork => 
        currentFilters.value.styles.includes(artwork.style)
      )
    }

    // Apply price range filter
    if (currentFilters.value.priceRange.min !== null || currentFilters.value.priceRange.max !== null) {
      filtered = filtered.filter(artwork => {
        const price = artwork.price
        const min = currentFilters.value.priceRange.min
        const max = currentFilters.value.priceRange.max
        
        if (min !== null && max !== null) {
          return price >= min && price <= max
        } else if (min !== null) {
          return price >= min
        } else if (max !== null) {
          return price <= max
        }
        return true
      })
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const field = currentSort.value.field
      const direction = currentSort.value.direction
      
      let aValue = a[field]
      let bValue = b[field]
      
      // Handle different data types
      if (field === 'price') {
        aValue = Number(aValue)
        bValue = Number(bValue)
      } else if (field === 'createdAt' || field === 'updatedAt') {
        aValue = new Date(aValue)
        bValue = new Date(bValue)
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }
      
      if (aValue < bValue) {
        return direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return direction === 'asc' ? 1 : -1
      }
      return 0
    })

    filteredArtworks.value = filtered
  }

  const setFilters = (filters) => {
    currentFilters.value = { ...filters }
    applyFiltersAndSort()
  }

  const setSort = (field, direction = 'asc') => {
    currentSort.value = { field, direction }
    applyFiltersAndSort()
  }

  const clearFilters = () => {
    currentFilters.value = {
      search: '',
      categories: [],
      artists: [],
      mediums: [],
      styles: [],
      priceRange: { min: null, max: null }
    }
    applyFiltersAndSort()
  }

  const getArtworkById = (id) => {
    return artworks.value.find(artwork => artwork.id === id)
  }

  const getRelatedArtworks = (artwork, limit = 4) => {
    return artworks.value
      .filter(item => 
        item.id !== artwork.id && 
        (item.artist === artwork.artist || 
         item.category === artwork.category || 
         item.style === artwork.style)
      )
      .slice(0, limit)
  }

  const getFeaturedArtworks = (limit = 6) => {
    // Return artworks sorted by price (highest first) as "featured"
    return [...artworks.value]
      .sort((a, b) => b.price - a.price)
      .slice(0, limit)
  }

  const getNewArrivals = (limit = 6) => {
    // Return newest artworks
    return [...artworks.value]
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, limit)
  }

  const addArtwork = async (artwork) => {
    loading.value = true
    error.value = null
    try {
      const newArtwork = await artworkService.create(artwork)
      artworks.value.push(newArtwork)
      return newArtwork
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateArtwork = async (id, artwork) => {
    loading.value = true
    error.value = null
    try {
      const updatedArtwork = await artworkService.update(id, artwork)
      const index = artworks.value.findIndex(a => a.id === id)
      if (index !== -1) {
        artworks.value[index] = updatedArtwork
      }
      return updatedArtwork
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteArtwork = async (id) => {
    loading.value = true
    error.value = null
    try {
      await artworkService.delete(id)
      artworks.value = artworks.value.filter(a => a.id !== id)
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    artworks,
    filteredArtworks,
    loading,
    error,
    currentFilters,
    currentSort,
    
    // Actions
    fetchArtworks,
    addArtwork,
    updateArtwork,
    deleteArtwork,
    setFilters,
    setSort,
    clearFilters,
    applyFiltersAndSort,
    
    // Getters
    getArtworkById,
    getRelatedArtworks,
    getFeaturedArtworks,
    getNewArrivals
  }
})