import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useCartStore = defineStore('cart', () => {
  const items = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Load cart from localStorage on initialization
  const loadCart = () => {
    try {
      const savedCart = localStorage.getItem('artstore_cart')
      if (savedCart) {
        items.value = JSON.parse(savedCart)
      }
    } catch (err) {
      console.error('Failed to load cart from localStorage:', err)
    }
  }

  // Save cart to localStorage
  const saveCart = () => {
    try {
      localStorage.setItem('artstore_cart', JSON.stringify(items.value))
    } catch (err) {
      console.error('Failed to save cart to localStorage:', err)
    }
  }

  // Computed properties
  const itemCount = computed(() => {
    return items.value.reduce((total, item) => total + item.quantity, 0)
  })

  const totalAmount = computed(() => {
    return items.value.reduce((total, item) => total + (item.artwork.price * item.quantity), 0)
  })

  const subtotal = computed(() => totalAmount.value)

  const tax = computed(() => totalAmount.value * 0.08) // 8% tax rate

  const finalTotal = computed(() => subtotal.value + tax.value)

  const isEmpty = computed(() => items.value.length === 0)

  // Actions
  const addItem = async (artwork, quantity = 1) => {
    loading.value = true
    error.value = null
    
    try {
      const existingItemIndex = items.value.findIndex(item => item.artwork.id === artwork.id)
      
      if (existingItemIndex !== -1) {
        // Item already exists, update quantity
        items.value[existingItemIndex].quantity += quantity
      } else {
        // Add new item
        items.value.push({
          id: `cart_${Date.now()}_${artwork.id}`,
          artwork,
          quantity,
          addedAt: new Date().toISOString()
        })
      }
      
      saveCart()
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const removeItem = (itemId) => {
    const index = items.value.findIndex(item => item.id === itemId)
    if (index !== -1) {
      items.value.splice(index, 1)
      saveCart()
    }
  }

  const updateQuantity = (itemId, quantity) => {
    if (quantity <= 0) {
      removeItem(itemId)
      return
    }

    const item = items.value.find(item => item.id === itemId)
    if (item) {
      item.quantity = quantity
      saveCart()
    }
  }

  const clearCart = () => {
    items.value = []
    saveCart()
  }

  const getItem = (artworkId) => {
    return items.value.find(item => item.artwork.id === artworkId)
  }

  const isInCart = (artworkId) => {
    return items.value.some(item => item.artwork.id === artworkId)
  }

  // Initialize cart from localStorage
  loadCart()

  return {
    // State
    items,
    loading,
    error,
    
    // Computed
    itemCount,
    totalAmount,
    subtotal,
    tax,
    finalTotal,
    isEmpty,
    
    // Actions
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    getItem,
    isInCart,
    loadCart,
    saveCart
  }
})