import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import paymentService from '../services/paymentService'
import { useCartStore } from './cart'

export const usePaymentStore = defineStore('payment', () => {
  // State
  const currentPayment = ref(null)
  const availableCurrencies = ref([])
  const selectedCurrency = ref(null)
  const paymentEstimate = ref(null)
  const paymentStatus = ref('idle') // idle, creating, pending, confirming, completed, failed, expired
  const loading = ref(false)
  const error = ref(null)
  const paymentHistory = ref([])

  // Payment flow state
  const currentStep = ref('method-selection') // method-selection, payment-details, payment-pending, confirmation
  const paymentMethod = ref('crypto') // crypto, traditional (for future expansion)
  
  // Load payment history from localStorage
  const loadPaymentHistory = () => {
    try {
      const saved = localStorage.getItem('artstore_payment_history')
      if (saved) {
        paymentHistory.value = JSON.parse(saved)
      }
    } catch (err) {
      console.error('Failed to load payment history:', err)
    }
  }

  // Save payment history to localStorage
  const savePaymentHistory = () => {
    try {
      localStorage.setItem('artstore_payment_history', JSON.stringify(paymentHistory.value))
    } catch (err) {
      console.error('Failed to save payment history:', err)
    }
  }

  // Computed properties
  const isPaymentActive = computed(() => {
    return currentPayment.value && ['creating', 'pending', 'confirming'].includes(paymentStatus.value)
  })

  const isPaymentExpired = computed(() => {
    if (!currentPayment.value?.expiresAt) return false
    return paymentService.isPaymentExpired(currentPayment.value.expiresAt)
  })

  const paymentTimeRemaining = computed(() => {
    if (!currentPayment.value?.expiresAt) return 0
    const remaining = new Date(currentPayment.value.expiresAt) - new Date()
    return Math.max(0, remaining)
  })

  const formattedPaymentAmount = computed(() => {
    if (!currentPayment.value) return ''
    return paymentService.formatCryptoAmount(
      currentPayment.value.payAmount,
      currentPayment.value.payCurrency
    )
  })

  // Actions
  const fetchAvailableCurrencies = async () => {
    loading.value = true
    error.value = null
    
    try {
      const currencies = await paymentService.getAvailableCurrencies()
      availableCurrencies.value = currencies
      
      // Set default currency to Bitcoin if available
      if (currencies.length > 0 && !selectedCurrency.value) {
        selectedCurrency.value = currencies.find(c => c.symbol === 'btc') || currencies[0]
      }
    } catch (err) {
      error.value = err.message
      console.error('Error fetching currencies:', err)
    } finally {
      loading.value = false
    }
  }

  const selectCurrency = (currency) => {
    selectedCurrency.value = currency
    // Clear previous estimate when currency changes
    paymentEstimate.value = null
  }

  const getPaymentEstimate = async (usdAmount) => {
    if (!selectedCurrency.value) {
      throw new Error('No currency selected')
    }

    loading.value = true
    error.value = null

    try {
      const estimate = await paymentService.getEstimatedAmount(
        usdAmount,
        selectedCurrency.value.symbol
      )
      paymentEstimate.value = estimate
      return estimate
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const createPayment = async (orderData) => {
    if (!selectedCurrency.value) {
      throw new Error('No payment currency selected')
    }

    loading.value = true
    paymentStatus.value = 'creating'
    error.value = null

    try {
      const cartStore = useCartStore()
      const orderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      const paymentData = {
        amount: orderData.totalAmount || cartStore.finalTotal,
        currency: selectedCurrency.value.symbol,
        orderId: orderId,
        customerEmail: orderData.customerEmail,
        description: `Contemporary Art Store - Order ${orderId}`,
        callbackUrl: `${window.location.origin}/api/payment-webhook` // This would need backend implementation
      }

      const payment = await paymentService.createPayment(paymentData)
      
      currentPayment.value = {
        ...payment,
        customerEmail: orderData.customerEmail,
        orderItems: [...cartStore.items],
        createdAt: new Date().toISOString()
      }
      
      paymentStatus.value = 'pending'
      currentStep.value = 'payment-pending'
      
      // Add to payment history
      paymentHistory.value.unshift({
        ...currentPayment.value,
        status: 'pending'
      })
      savePaymentHistory()
      
      return currentPayment.value
    } catch (err) {
      error.value = err.message
      paymentStatus.value = 'failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const checkPaymentStatus = async (paymentId = null) => {
    const id = paymentId || currentPayment.value?.paymentId
    if (!id) {
      throw new Error('No payment ID provided')
    }

    try {
      const status = await paymentService.getPaymentStatus(id)
      
      // Update current payment if it matches
      if (currentPayment.value?.paymentId === id) {
        currentPayment.value = {
          ...currentPayment.value,
          ...status
        }
        paymentStatus.value = mapPaymentStatus(status.paymentStatus)
      }
      
      // Update payment history
      const historyIndex = paymentHistory.value.findIndex(p => p.paymentId === id)
      if (historyIndex !== -1) {
        paymentHistory.value[historyIndex] = {
          ...paymentHistory.value[historyIndex],
          ...status,
          status: mapPaymentStatus(status.paymentStatus)
        }
        savePaymentHistory()
      }
      
      return status
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const processWebhookUpdate = (webhookData) => {
    const update = paymentService.processWebhookNotification(webhookData)
    
    // Update current payment if it matches
    if (currentPayment.value?.paymentId === update.paymentId) {
      currentPayment.value = {
        ...currentPayment.value,
        ...update
      }
      paymentStatus.value = mapPaymentStatus(update.paymentStatus)
      
      // Move to confirmation step if payment is completed
      if (paymentStatus.value === 'completed') {
        currentStep.value = 'confirmation'
        // Clear cart on successful payment
        const cartStore = useCartStore()
        cartStore.clearCart()
      }
    }
    
    // Update payment history
    const historyIndex = paymentHistory.value.findIndex(p => p.paymentId === update.paymentId)
    if (historyIndex !== -1) {
      paymentHistory.value[historyIndex] = {
        ...paymentHistory.value[historyIndex],
        ...update,
        status: mapPaymentStatus(update.paymentStatus)
      }
      savePaymentHistory()
    }
  }

  const cancelPayment = () => {
    if (currentPayment.value) {
      paymentStatus.value = 'failed'
      currentStep.value = 'method-selection'
      currentPayment.value = null
      paymentEstimate.value = null
    }
  }

  const resetPaymentFlow = () => {
    currentPayment.value = null
    paymentEstimate.value = null
    paymentStatus.value = 'idle'
    currentStep.value = 'method-selection'
    selectedCurrency.value = null
    error.value = null
  }

  const setPaymentStep = (step) => {
    currentStep.value = step
  }

  const getPaymentById = (paymentId) => {
    return paymentHistory.value.find(p => p.paymentId === paymentId)
  }

  const getOrderHistory = () => {
    return paymentHistory.value.filter(p => p.status === 'completed')
  }

  // Helper function to map NOWPayments status to our internal status
  const mapPaymentStatus = (nowPaymentsStatus) => {
    const statusMap = {
      'waiting': 'pending',
      'confirming': 'confirming',
      'confirmed': 'completed',
      'sending': 'confirming',
      'partially_paid': 'pending',
      'finished': 'completed',
      'failed': 'failed',
      'refunded': 'failed',
      'expired': 'expired'
    }
    
    return statusMap[nowPaymentsStatus] || 'pending'
  }

  // Auto-load currencies and payment history on store initialization
  fetchAvailableCurrencies()
  loadPaymentHistory()

  // Set up periodic status checking for active payments
  let statusCheckInterval = null
  const startStatusChecking = () => {
    if (statusCheckInterval) return
    
    statusCheckInterval = setInterval(async () => {
      if (isPaymentActive.value && currentPayment.value) {
        try {
          await checkPaymentStatus()
        } catch (err) {
          console.error('Status check failed:', err)
        }
      }
    }, 30000) // Check every 30 seconds
  }

  const stopStatusChecking = () => {
    if (statusCheckInterval) {
      clearInterval(statusCheckInterval)
      statusCheckInterval = null
    }
  }

  return {
    // State
    currentPayment,
    availableCurrencies,
    selectedCurrency,
    paymentEstimate,
    paymentStatus,
    loading,
    error,
    paymentHistory,
    currentStep,
    paymentMethod,
    
    // Computed
    isPaymentActive,
    isPaymentExpired,
    paymentTimeRemaining,
    formattedPaymentAmount,
    
    // Actions
    fetchAvailableCurrencies,
    selectCurrency,
    getPaymentEstimate,
    createPayment,
    checkPaymentStatus,
    processWebhookUpdate,
    cancelPayment,
    resetPaymentFlow,
    setPaymentStep,
    getPaymentById,
    getOrderHistory,
    startStatusChecking,
    stopStatusChecking
  }
})