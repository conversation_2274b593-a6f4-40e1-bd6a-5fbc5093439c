import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authService } from '../services/authService'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const isAuthenticated = ref(false)
  const loading = ref(false)
  const error = ref(null)
  const token = ref(null)

  // Computed getters for role-based access
  const userRole = computed(() => user.value?.role || null)
  const isAdmin = computed(() => userRole.value === 'admin')
  const isCurator = computed(() => userRole.value === 'curator')
  const isCustomer = computed(() => userRole.value === 'customer')
  const isStaff = computed(() => isAdmin.value || isCurator.value)

  // Check if user has required role
  const hasRole = (requiredRole) => {
    if (!user.value || !requiredRole) return false
    if (Array.isArray(requiredRole)) {
      return requiredRole.includes(userRole.value)
    }
    return userRole.value === requiredRole
  }

  // Check if user has permission for specific action
  const hasPermission = (permission) => {
    if (!user.value) return false
    
    const permissions = {
      'view_admin': ['admin', 'curator'],
      'manage_artworks': ['admin', 'curator'],
      'manage_orders': ['admin'],
      'manage_users': ['admin'],
      'view_gallery': ['admin', 'curator', 'customer'],
      'purchase_artwork': ['customer']
    }
    
    return permissions[permission]?.includes(userRole.value) || false
  }

  // Initialize auth state from localStorage with token validation
  const initAuth = () => {
    const storedAuth = localStorage.getItem('isAuthenticated')
    const storedUser = localStorage.getItem('user')
    const storedToken = localStorage.getItem('token')
    
    if (storedAuth === 'true' && storedUser && storedToken) {
      try {
        const parsedUser = JSON.parse(storedUser)
        // Validate token expiration (mock validation for demo)
        const tokenData = JSON.parse(atob(storedToken.split('.')[1] || '{}'))
        const isTokenValid = tokenData.exp ? tokenData.exp > Date.now() / 1000 : true
        
        if (isTokenValid) {
          isAuthenticated.value = true
          user.value = parsedUser
          token.value = storedToken
        } else {
          // Token expired, clear auth state
          clearAuthState()
        }
      } catch (error) {
        // Invalid stored data, clear auth state
        clearAuthState()
      }
    }
  }

  // Clear authentication state
  const clearAuthState = () => {
    user.value = null
    isAuthenticated.value = false
    token.value = null
    error.value = null
    
    localStorage.removeItem('isAuthenticated')
    localStorage.removeItem('user')
    localStorage.removeItem('token')
  }

  const login = async (email, password) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await authService.login(email, password)
      user.value = response.user
      token.value = response.token
      isAuthenticated.value = true
      
      // Store auth state in localStorage
      localStorage.setItem('isAuthenticated', 'true')
      localStorage.setItem('user', JSON.stringify(response.user))
      localStorage.setItem('token', response.token)
      
      return response
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    loading.value = true
    
    try {
      await authService.logout()
    } catch (err) {
      console.warn('Logout service error:', err.message)
    } finally {
      clearAuthState()
      loading.value = false
    }
  }

  const register = async (userData) => {
    loading.value = true
    error.value = null
    
    try {
      // Validate password strength
      if (!isPasswordValid(userData.password)) {
        throw new Error('Password must be at least 8 characters long and contain uppercase, lowercase, and numbers')
      }
      
      const response = await authService.register(userData)
      user.value = response.user
      token.value = response.token
      isAuthenticated.value = true
      
      // Store auth state in localStorage
      localStorage.setItem('isAuthenticated', 'true')
      localStorage.setItem('user', JSON.stringify(response.user))
      localStorage.setItem('token', response.token)
      
      return response
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateProfile = async (profileData) => {
    loading.value = true
    error.value = null
    
    try {
      const updatedUser = await authService.updateProfile(user.value.id, profileData)
      user.value = updatedUser
      
      // Update stored user data
      localStorage.setItem('user', JSON.stringify(updatedUser))
      
      return updatedUser
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  // Password validation helper
  const isPasswordValid = (password) => {
    const minLength = 8
    const hasUpperCase = /[A-Z]/.test(password)
    const hasLowerCase = /[a-z]/.test(password)
    const hasNumbers = /\d/.test(password)
    
    return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers
  }

  // Initialize auth state when store is created
  initAuth()

  return {
    // State
    user,
    isAuthenticated,
    loading,
    error,
    token,
    
    // Computed getters
    userRole,
    isAdmin,
    isCurator,
    isCustomer,
    isStaff,
    
    // Actions
    login,
    logout,
    register,
    updateProfile,
    initAuth,
    clearAuthState,
    
    // Utility functions
    hasRole,
    hasPermission,
    isPasswordValid
  }
})