import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useWishlistStore = defineStore('wishlist', () => {
  const items = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Load wishlist from localStorage on initialization
  const loadWishlist = () => {
    try {
      const savedWishlist = localStorage.getItem('artstore_wishlist')
      if (savedWishlist) {
        items.value = JSON.parse(savedWishlist)
      }
    } catch (err) {
      console.error('Failed to load wishlist from localStorage:', err)
    }
  }

  // Save wishlist to localStorage
  const saveWishlist = () => {
    try {
      localStorage.setItem('artstore_wishlist', JSON.stringify(items.value))
    } catch (err) {
      console.error('Failed to save wishlist to localStorage:', err)
    }
  }

  // Computed properties
  const itemCount = computed(() => items.value.length)

  const isEmpty = computed(() => items.value.length === 0)

  const totalValue = computed(() => {
    return items.value.reduce((total, item) => total + item.artwork.price, 0)
  })

  // Actions
  const addItem = (artwork) => {
    loading.value = true
    error.value = null
    
    try {
      // Check if item already exists
      const existingItem = items.value.find(item => item.artwork.id === artwork.id)
      
      if (!existingItem) {
        items.value.push({
          id: `wishlist_${Date.now()}_${artwork.id}`,
          artwork,
          addedAt: new Date().toISOString()
        })
        saveWishlist()
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const removeItem = (artworkId) => {
    const index = items.value.findIndex(item => item.artwork.id === artworkId)
    if (index !== -1) {
      items.value.splice(index, 1)
      saveWishlist()
    }
  }

  const clearWishlist = () => {
    items.value = []
    saveWishlist()
  }

  const getItem = (artworkId) => {
    return items.value.find(item => item.artwork.id === artworkId)
  }

  const isInWishlist = (artworkId) => {
    return items.value.some(item => item.artwork.id === artworkId)
  }

  const toggleItem = (artwork) => {
    if (isInWishlist(artwork.id)) {
      removeItem(artwork.id)
    } else {
      addItem(artwork)
    }
  }

  // Get wishlist items sorted by date added (newest first)
  const getSortedItems = computed(() => {
    return [...items.value].sort((a, b) => new Date(b.addedAt) - new Date(a.addedAt))
  })

  // Initialize wishlist from localStorage
  loadWishlist()

  return {
    // State
    items,
    loading,
    error,
    
    // Computed
    itemCount,
    isEmpty,
    totalValue,
    getSortedItems,
    
    // Actions
    addItem,
    removeItem,
    clearWishlist,
    getItem,
    isInWishlist,
    toggleItem,
    loadWishlist,
    saveWishlist
  }
})