<template>
  <div id="app">
    <!-- Routes that don't need layout (login, register) -->
    <template v-if="isAuthPage">
      <RouterView />
    </template>
    
    <!-- Routes that use the main layout -->
    <template v-else>
      <AppLayout>
        <RouterView />
      </AppLayout>
    </template>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { RouterView, useRoute } from 'vue-router'
import { useAuthStore } from './stores/auth'
import AppLayout from './components/AppLayout.vue'

const route = useRoute()
const authStore = useAuthStore()

// Determine if current route is an auth page (login/register)
const isAuthPage = computed(() => {
  return ['login', 'register'].includes(route.name)
})

// Initialize authentication state on app mount
onMounted(() => {
  authStore.initAuth()
})
</script>

<style>
#app {
  font-family: 'Inter', Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus styles for accessibility */
.focus\:ring-primary-500:focus {
  --tw-ring-color: rgb(99 102 241);
}

.focus\:border-primary-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241);
}

/* Primary color utilities */
.bg-primary-50 {
  background-color: rgb(238 242 255);
}

.bg-primary-100 {
  background-color: rgb(224 231 255);
}

.bg-primary-600 {
  background-color: rgb(99 102 241);
}

.bg-primary-700 {
  background-color: rgb(79 70 229);
}

.text-primary-400 {
  color: rgb(129 140 248);
}

.text-primary-500 {
  color: rgb(99 102 241);
}

.text-primary-600 {
  color: rgb(99 102 241);
}

.text-primary-700 {
  color: rgb(79 70 229);
}

.border-primary-500 {
  border-color: rgb(99 102 241);
}

.hover\:bg-primary-700:hover {
  background-color: rgb(79 70 229);
}

.hover\:text-primary-500:hover {
  color: rgb(99 102 241);
}

.hover\:text-primary-600:hover {
  color: rgb(99 102 241);
}

/* Transition utilities */
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
</style>