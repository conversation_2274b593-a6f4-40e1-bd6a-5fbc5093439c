<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Shopping Cart</h1>
        <p class="mt-2 text-gray-600">Review your selected artworks before checkout</p>
      </div>

      <!-- Empty Cart State -->
      <div v-if="isEmpty" class="text-center py-12">
        <div class="text-gray-400 mb-6">
          <svg class="mx-auto h-24 w-24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15.5M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z" />
          </svg>
        </div>
        <h3 class="text-xl font-medium text-gray-900 mb-2">Your cart is empty</h3>
        <p class="text-gray-600 mb-6">Discover amazing artworks in our gallery</p>
        <RouterLink 
          to="/gallery"
          class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Browse Gallery
        </RouterLink>
      </div>

      <!-- Cart Content -->
      <div v-else class="lg:grid lg:grid-cols-12 lg:gap-8">
        <!-- Cart Items -->
        <div class="lg:col-span-8">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">
                Cart Items ({{ itemCount }})
              </h2>
            </div>
            
            <div class="divide-y divide-gray-200">
              <CartItem 
                v-for="item in items" 
                :key="item.id"
                :item="item"
                @update-quantity="updateQuantity"
                @remove-item="removeItem"
              />
            </div>
          </div>

          <!-- Continue Shopping -->
          <div class="mt-6">
            <RouterLink 
              to="/gallery"
              class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
              Continue Shopping
            </RouterLink>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-4 mt-8 lg:mt-0">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 sticky top-8">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">Order Summary</h2>
            </div>
            
            <div class="px-6 py-4 space-y-4">
              <!-- Subtotal -->
              <div class="flex justify-between text-base">
                <span class="text-gray-600">Subtotal</span>
                <span class="font-medium text-gray-900">${{ formatPrice(subtotal) }}</span>
              </div>
              
              <!-- Tax -->
              <div class="flex justify-between text-base">
                <span class="text-gray-600">Tax (8%)</span>
                <span class="font-medium text-gray-900">${{ formatPrice(tax) }}</span>
              </div>
              
              <!-- Shipping -->
              <div class="flex justify-between text-base">
                <span class="text-gray-600">Shipping</span>
                <span class="font-medium text-green-600">Free</span>
              </div>
              
              <hr class="border-gray-200">
              
              <!-- Total -->
              <div class="flex justify-between text-lg font-bold">
                <span class="text-gray-900">Total</span>
                <span class="text-gray-900">${{ formatPrice(finalTotal) }}</span>
              </div>
            </div>

            <!-- Checkout Actions -->
            <div class="px-6 py-4 border-t border-gray-200">
              <button 
                @click="proceedToCheckout"
                :disabled="isProcessing || paymentStore.loading"
                class="w-full bg-blue-600 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 mb-3"
              >
                {{ isProcessing || paymentStore.loading ? 'Processing...' : 'Proceed to Checkout' }}
              </button>
              
              <!-- Payment Methods -->
              <div class="text-center">
                <p class="text-sm text-gray-600 mb-2">We accept cryptocurrency payments</p>
                <div class="flex justify-center space-x-2">
                  <div v-for="currency in availableCurrencies.slice(0, 3)" :key="currency.symbol" 
                       class="flex items-center px-2 py-1 bg-blue-100 rounded text-xs font-medium text-blue-800">
                    {{ currency.icon }} {{ currency.name }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Security Features -->
            <div class="px-6 py-4 bg-gray-50 rounded-b-lg">
              <div class="space-y-2 text-sm text-gray-600">
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  Secure checkout
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Certificate of authenticity included
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                  Insured shipping worldwide
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recently Viewed -->
      <div v-if="!isEmpty && recentlyViewed.length > 0" class="mt-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">You might also like</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <ProductCard 
            v-for="artwork in recentlyViewed" 
            :key="artwork.id"
            :artwork="artwork"
          />
        </div>
      </div>
    </div>

    <!-- Checkout Modal -->
    <div v-if="showCheckoutModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="closeCheckoutModal"></div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <!-- Modal Header -->
          <div class="bg-white px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">
                {{ getStepTitle() }}
              </h3>
              <button
                @click="closeCheckoutModal"
                class="text-gray-400 hover:text-gray-600"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <!-- Progress Steps -->
            <div class="mt-4">
              <div class="flex items-center">
                <div v-for="(step, index) in checkoutSteps" :key="step.id" class="flex items-center">
                  <div :class="[
                    'flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium',
                    getCurrentStepIndex() > index ? 'bg-green-500 text-white' :
                    getCurrentStepIndex() === index ? 'bg-blue-500 text-white' :
                    'bg-gray-200 text-gray-600'
                  ]">
                    <svg v-if="getCurrentStepIndex() > index" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span v-else>{{ index + 1 }}</span>
                  </div>
                  <span class="ml-2 text-sm font-medium text-gray-900">{{ step.title }}</span>
                  <div v-if="index < checkoutSteps.length - 1" class="flex-1 mx-4 h-0.5 bg-gray-200">
                    <div :class="[
                      'h-full transition-all duration-300',
                      getCurrentStepIndex() > index ? 'bg-green-500' : 'bg-gray-200'
                    ]"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Modal Content -->
          <div class="bg-white px-6 py-6 max-h-96 overflow-y-auto">
            <!-- Payment Method Selection -->
            <PaymentMethod
              v-if="paymentStore.currentStep === 'method-selection'"
              :total-amount="finalTotal"
              @back="closeCheckoutModal"
              @continue="handlePaymentMethodSelected"
            />

            <!-- Crypto Payment Interface -->
            <CryptoPayment
              v-else-if="paymentStore.currentStep === 'payment-pending'"
              :payment-data="selectedPaymentData"
              @payment-completed="handlePaymentCompleted"
              @payment-failed="handlePaymentFailed"
              @payment-cancelled="handlePaymentCancelled"
            />

            <!-- Order Confirmation -->
            <OrderConfirmation
              v-else-if="paymentStore.currentStep === 'confirmation'"
              :order-id="paymentStore.currentPayment?.orderId"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { RouterLink, useRouter } from 'vue-router'
import { useCartStore } from '../stores/cart'
import { usePaymentStore } from '../stores/payment'
import { useArtworkStore } from '../stores/artwork'
import { useAuthStore } from '../stores/auth'
import CartItem from '../components/CartItem.vue'
import ProductCard from '../components/ProductCard.vue'
import PaymentMethod from '../components/PaymentMethod.vue'
import CryptoPayment from '../components/CryptoPayment.vue'
import OrderConfirmation from '../components/OrderConfirmation.vue'

const router = useRouter()
const cartStore = useCartStore()
const paymentStore = usePaymentStore()
const artworkStore = useArtworkStore()
const authStore = useAuthStore()

// Reactive state
const isProcessing = ref(false)
const showCheckoutModal = ref(false)
const selectedPaymentData = ref(null)

// Checkout steps configuration
const checkoutSteps = [
  { id: 'method-selection', title: 'Payment Method' },
  { id: 'payment-pending', title: 'Payment' },
  { id: 'confirmation', title: 'Confirmation' }
]

// Computed properties
const items = computed(() => cartStore.items)
const itemCount = computed(() => cartStore.itemCount)
const subtotal = computed(() => cartStore.subtotal)
const tax = computed(() => cartStore.tax)
const finalTotal = computed(() => cartStore.finalTotal)
const isEmpty = computed(() => cartStore.isEmpty)
const availableCurrencies = computed(() => paymentStore.availableCurrencies)

const recentlyViewed = computed(() => {
  // Get some featured artworks as "recently viewed" for demo
  return artworkStore.getFeaturedArtworks(4)
})

// Methods
const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US').format(price)
}

const updateQuantity = (itemId, quantity) => {
  cartStore.updateQuantity(itemId, quantity)
}

const removeItem = (itemId) => {
  cartStore.removeItem(itemId)
}

const proceedToCheckout = () => {
  // Reset payment flow
  paymentStore.resetPaymentFlow()
  showCheckoutModal.value = true
}

const closeCheckoutModal = () => {
  showCheckoutModal.value = false
  // Stop any ongoing payment processes
  paymentStore.stopStatusChecking()
}

const getStepTitle = () => {
  const currentStep = paymentStore.currentStep
  const step = checkoutSteps.find(s => s.id === currentStep)
  return step ? step.title : 'Checkout'
}

const getCurrentStepIndex = () => {
  const currentStep = paymentStore.currentStep
  return checkoutSteps.findIndex(s => s.id === currentStep)
}

const handlePaymentMethodSelected = async (paymentData) => {
  selectedPaymentData.value = paymentData
  isProcessing.value = true
  
  try {
    // Create the payment
    const orderData = {
      totalAmount: finalTotal.value,
      customerEmail: authStore.user?.email || '<EMAIL>',
      orderItems: [...cartStore.items]
    }
    
    await paymentStore.createPayment(orderData)
    paymentStore.startStatusChecking()
  } catch (error) {
    console.error('Failed to create payment:', error)
    // Handle error - could show error message
  } finally {
    isProcessing.value = false
  }
}

const handlePaymentCompleted = (paymentData) => {
  console.log('Payment completed:', paymentData)
  // Payment completed, OrderConfirmation component will be shown automatically
  // Cart will be cleared by the payment store
}

const handlePaymentFailed = (paymentData) => {
  console.log('Payment failed:', paymentData)
  // Reset to payment method selection
  paymentStore.setPaymentStep('method-selection')
}

const handlePaymentCancelled = () => {
  console.log('Payment cancelled')
  // Close modal and reset
  closeCheckoutModal()
  paymentStore.resetPaymentFlow()
}

// Watch for payment completion to potentially redirect
watch(() => paymentStore.paymentStatus, (newStatus) => {
  if (newStatus === 'completed' && paymentStore.currentPayment) {
    // Could redirect to a dedicated order confirmation page
    // router.push(`/order-confirmation/${paymentStore.currentPayment.orderId}`)
  }
})

// Initialize data
onMounted(async () => {
  if (artworkStore.artworks.length === 0) {
    await artworkStore.fetchArtworks()
  }
  
  // Initialize payment currencies
  if (paymentStore.availableCurrencies.length === 0) {
    await paymentStore.fetchAvailableCurrencies()
  }
})
</script>