<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
          <h1 class="text-4xl font-bold text-gray-900 mb-2">Art Gallery</h1>
          <p class="text-lg text-gray-600 mb-6">Discover our curated collection of contemporary artworks</p>
          
          <!-- Search Bar -->
          <div class="max-w-2xl mx-auto">
            <SearchBar 
              v-model="searchQuery"
              @search="handleSearch"
              placeholder="Search artworks, artists, styles..."
            />
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="lg:grid lg:grid-cols-4 lg:gap-8">
        <!-- Filters Sidebar -->
        <div class="lg:col-span-1">
          <div class="sticky top-8">
            <FilterPanel @filtersChanged="handleFiltersChanged" />
          </div>
        </div>

        <!-- Main Content -->
        <div class="lg:col-span-3 mt-8 lg:mt-0">
          <!-- Results Header -->
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
            <div class="mb-4 sm:mb-0">
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ filteredArtworks.length }}</span> 
                of <span class="font-medium">{{ totalArtworks }}</span> artworks
              </p>
            </div>
            
            <!-- Sort Options -->
            <div class="flex items-center space-x-4">
              <label class="text-sm font-medium text-gray-700">Sort by:</label>
              <select 
                v-model="selectedSort"
                @change="handleSortChange"
                class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="createdAt-desc">Newest First</option>
                <option value="createdAt-asc">Oldest First</option>
                <option value="price-asc">Price: Low to High</option>
                <option value="price-desc">Price: High to Low</option>
                <option value="title-asc">Title: A to Z</option>
                <option value="title-desc">Title: Z to A</option>
                <option value="artist-asc">Artist: A to Z</option>
                <option value="artist-desc">Artist: Z to A</option>
              </select>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="loading" class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="text-center py-12">
            <div class="text-red-600 mb-4">
              <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Error loading artworks</h3>
            <p class="text-gray-600 mb-4">{{ error }}</p>
            <button 
              @click="retryLoad"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Try Again
            </button>
          </div>

          <!-- No Results -->
          <div v-else-if="filteredArtworks.length === 0" class="text-center py-12">
            <div class="text-gray-400 mb-4">
              <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No artworks found</h3>
            <p class="text-gray-600 mb-4">Try adjusting your search or filter criteria</p>
            <button 
              @click="clearAllFilters"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Clear all filters
            </button>
          </div>

          <!-- Artworks Grid -->
          <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <ProductCard 
              v-for="artwork in filteredArtworks" 
              :key="artwork.id"
              :artwork="artwork"
            />
          </div>

          <!-- Load More Button (for future pagination) -->
          <div v-if="filteredArtworks.length > 0 && filteredArtworks.length >= 12" class="text-center mt-12">
            <button class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Load More Artworks
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useArtworkStore } from '../stores/artwork'
import ProductCard from '../components/ProductCard.vue'
import FilterPanel from '../components/FilterPanel.vue'
import SearchBar from '../components/SearchBar.vue'

const artworkStore = useArtworkStore()

// Reactive state
const searchQuery = ref('')
const selectedSort = ref('createdAt-desc')

// Computed properties
const filteredArtworks = computed(() => artworkStore.filteredArtworks)
const totalArtworks = computed(() => artworkStore.artworks.length)
const loading = computed(() => artworkStore.loading)
const error = computed(() => artworkStore.error)

// Methods
const handleSearch = (query) => {
  const currentFilters = { ...artworkStore.currentFilters }
  currentFilters.search = query
  artworkStore.setFilters(currentFilters)
}

const handleFiltersChanged = (filters) => {
  // Merge search query with other filters
  const mergedFilters = {
    ...filters,
    search: searchQuery.value
  }
  artworkStore.setFilters(mergedFilters)
}

const handleSortChange = () => {
  const [field, direction] = selectedSort.value.split('-')
  artworkStore.setSort(field, direction)
}

const clearAllFilters = () => {
  searchQuery.value = ''
  artworkStore.clearFilters()
}

const retryLoad = async () => {
  await artworkStore.fetchArtworks()
}

// Initialize data
onMounted(async () => {
  await artworkStore.fetchArtworks()
  
  // Set initial sort
  const [field, direction] = selectedSort.value.split('-')
  artworkStore.setSort(field, direction)
})

// Watch for search query changes
watch(searchQuery, (newQuery) => {
  handleSearch(newQuery)
})
</script>