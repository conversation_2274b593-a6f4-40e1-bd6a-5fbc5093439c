<template>
  <div class="admin-dashboard">
    <!-- Mobile Sidebar Overlay -->
    <div
      v-if="sidebarOpen"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 z-40 lg:hidden"
      @click="sidebarOpen = false"
    ></div>

    <!-- Sidebar -->
    <div :class="[
      'fixed inset-y-0 left-0 z-50 lg:z-auto lg:static lg:inset-auto',
      'transform transition-transform duration-300 ease-in-out lg:transform-none',
      sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
    ]">
      <AdminSidebar
        :active-section="activeSection"
        @section-changed="handleSectionChange"
      />
    </div>

    <!-- Main Content -->
    <div class="lg:ml-64 flex flex-col min-h-screen">
      <!-- Header -->
      <AdminHeader
        :current-section="activeSection"
        @toggle-sidebar="sidebarOpen = !sidebarOpen"
        @quick-action="handleQuickAction"
      />

      <!-- Content Area -->
      <main class="flex-1 bg-gray-50 p-6">
        <!-- Dashboard Overview -->
        <div v-if="activeSection === 'dashboard'" class="space-y-6">
          <!-- Welcome Section -->
          <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-2xl font-bold text-gray-900">Welcome back, {{ userName }}!</h1>
                <p class="text-gray-600 mt-1">Here's what's happening with your art store today.</p>
              </div>
              <div class="text-right">
                <p class="text-sm text-gray-500">{{ currentDate }}</p>
                <p class="text-sm text-gray-500">{{ currentTime }}</p>
              </div>
            </div>
          </div>

          <!-- Key Metrics -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Total Artworks</p>
                  <p class="text-2xl font-semibold text-gray-900">{{ dashboardStats.totalArtworks }}</p>
                </div>
              </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Total Orders</p>
                  <p class="text-2xl font-semibold text-gray-900">{{ dashboardStats.totalOrders }}</p>
                </div>
              </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                  <p class="text-2xl font-semibold text-gray-900">${{ dashboardStats.totalRevenue.toLocaleString() }}</p>
                </div>
              </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-500">Total Users</p>
                  <p class="text-2xl font-semibold text-gray-900">{{ dashboardStats.totalUsers }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Orders -->
            <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Recent Orders</h3>
                <button
                  @click="activeSection = 'orders'"
                  class="text-sm text-primary-600 hover:text-primary-500"
                >
                  View All
                </button>
              </div>
              <div class="space-y-3">
                <div
                  v-for="order in recentOrders"
                  :key="order.id"
                  class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <p class="text-sm font-medium text-gray-900">Order #{{ order.id }}</p>
                    <p class="text-sm text-gray-500">{{ order.customerName }}</p>
                  </div>
                  <div class="text-right">
                    <p class="text-sm font-medium text-gray-900">${{ order.totalAmount.toLocaleString() }}</p>
                    <span :class="[
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      order.status === 'completed' ? 'bg-green-100 text-green-800' :
                      order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    ]">
                      {{ order.status }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div class="grid grid-cols-2 gap-3">
                <button
                  @click="handleQuickAction('add-artwork')"
                  class="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <div class="text-center">
                    <svg class="w-6 h-6 text-gray-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-900">Add Artwork</span>
                  </div>
                </button>
                
                <button
                  @click="activeSection = 'orders'"
                  class="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <div class="text-center">
                    <svg class="w-6 h-6 text-gray-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-900">View Orders</span>
                  </div>
                </button>
                
                <button
                  @click="activeSection = 'analytics'"
                  class="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <div class="text-center">
                    <svg class="w-6 h-6 text-gray-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-900">Analytics</span>
                  </div>
                </button>
                
                <button
                  @click="activeSection = 'settings'"
                  class="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <div class="text-center">
                    <svg class="w-6 h-6 text-gray-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-900">Settings</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Artwork Management -->
        <div v-else-if="activeSection === 'artworks'">
          <ArtworkList
            :artworks="artworks"
            @add-artwork="showArtworkForm = true"
            @edit-artwork="editArtwork"
            @delete-artwork="deleteArtwork"
          />
        </div>

        <!-- Order Management -->
        <div v-else-if="activeSection === 'orders'">
          <OrderManagement
            :orders="orders"
            @view-order="viewOrderDetail"
            @update-status="updateOrderStatus"
          />
        </div>

        <!-- User Management -->
        <div v-else-if="activeSection === 'users'" v-show="canManageUsers">
          <UserManagement
            :users="users"
            @update-user-status="updateUserStatus"
            @update-user-role="updateUserRole"
            @delete-user="deleteUser"
          />
        </div>

        <!-- Analytics -->
        <div v-else-if="activeSection === 'analytics'">
          <Analytics />
        </div>

        <!-- Categories Management -->
        <div v-else-if="activeSection === 'categories'">
          <Settings />
        </div>

        <!-- Artists Management -->
        <div v-else-if="activeSection === 'artists'">
          <Settings />
        </div>

        <!-- Settings -->
        <div v-else-if="activeSection === 'settings'" v-show="canManageSettings">
          <Settings />
        </div>
      </main>
    </div>

    <!-- Artwork Form Modal -->
    <div v-if="showArtworkForm" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-4 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white">
        <ArtworkForm
          :artwork="selectedArtwork"
          @close="closeArtworkForm"
          @submit="handleArtworkSubmit"
        />
      </div>
    </div>

    <!-- Order Detail Modal -->
    <div v-if="showOrderDetail" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-4 mx-auto p-5 border max-w-6xl shadow-lg rounded-md bg-white">
        <OrderDetail
          :order="selectedOrder"
          @close="closeOrderDetail"
          @update-status="updateOrderStatus"
          @cancel-order="cancelOrder"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useArtworkStore } from '../stores/artwork'
import { useAuthStore } from '../stores/auth'
import { useAdminStore } from '../stores/admin'

// Components
import AdminSidebar from '../components/AdminSidebar.vue'
import AdminHeader from '../components/AdminHeader.vue'
import ArtworkList from '../components/ArtworkList.vue'
import ArtworkForm from '../components/ArtworkForm.vue'
import OrderManagement from '../components/OrderManagement.vue'
import OrderDetail from '../components/OrderDetail.vue'
import UserManagement from '../components/UserManagement.vue'
import Analytics from '../components/Analytics.vue'
import Settings from '../components/Settings.vue'

// Router and stores
const router = useRouter()
const artworkStore = useArtworkStore()
const authStore = useAuthStore()
const adminStore = useAdminStore()

// Reactive state
const activeSection = ref('dashboard')
const sidebarOpen = ref(false)
const showArtworkForm = ref(false)
const showOrderDetail = ref(false)
const selectedArtwork = ref(null)
const selectedOrder = ref(null)

// Data
const artworks = ref([])
const orders = ref([])
const users = ref([])

// Computed properties
const userName = computed(() => authStore.user?.name || 'Admin')
const canManageUsers = computed(() => authStore.hasPermission('manage_users'))
const canManageSettings = computed(() => authStore.isAdmin)

const currentDate = computed(() => {
  return new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

const currentTime = computed(() => {
  return new Date().toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  })
})

const dashboardStats = computed(() => ({
  totalArtworks: artworks.value.length,
  totalOrders: orders.value.length,
  totalRevenue: orders.value
    .filter(order => order.status === 'completed')
    .reduce((sum, order) => sum + order.totalAmount, 0),
  totalUsers: users.value.length
}))

const recentOrders = computed(() => {
  return [...orders.value]
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, 5)
})

// Methods
const handleSectionChange = (section) => {
  activeSection.value = section
  sidebarOpen.value = false // Close mobile sidebar
}

const handleQuickAction = (action) => {
  switch (action) {
    case 'add-artwork':
      selectedArtwork.value = null
      showArtworkForm.value = true
      break
    default:
      console.log('Quick action:', action)
  }
}

const editArtwork = (artwork) => {
  selectedArtwork.value = artwork
  showArtworkForm.value = true
}

const closeArtworkForm = () => {
  showArtworkForm.value = false
  selectedArtwork.value = null
}

const handleArtworkSubmit = async (artworkData) => {
  try {
    if (selectedArtwork.value) {
      // Update existing artwork
      await artworkStore.updateArtwork(selectedArtwork.value.id, artworkData)
    } else {
      // Add new artwork
      await artworkStore.addArtwork(artworkData)
    }
    
    // Refresh artworks list
    await artworkStore.fetchArtworks()
    artworks.value = artworkStore.artworks
    
    closeArtworkForm()
  } catch (error) {
    console.error('Error saving artwork:', error)
  }
}

const deleteArtwork = async (artworkId) => {
  try {
    await artworkStore.deleteArtwork(artworkId)
    artworks.value = artworkStore.artworks
  } catch (error) {
    console.error('Error deleting artwork:', error)
  }
}

const viewOrderDetail = (order) => {
  selectedOrder.value = order
  showOrderDetail.value = true
}

const closeOrderDetail = () => {
  showOrderDetail.value = false
  selectedOrder.value = null
}

const updateOrderStatus = async ({ orderId, status }) => {
  try {
    await adminStore.updateOrderStatus(orderId, status)
    await adminStore.fetchOrders()
    orders.value = adminStore.orders
  } catch (error) {
    console.error('Error updating order status:', error)
  }
}

const cancelOrder = async (orderId) => {
  try {
    await adminStore.updateOrderStatus(orderId, 'cancelled')
    await adminStore.fetchOrders()
    orders.value = adminStore.orders
  } catch (error) {
    console.error('Error cancelling order:', error)
  }
}

const updateUserStatus = async ({ userId, status }) => {
  try {
    await adminStore.updateUserStatus(userId, status)
    await adminStore.fetchUsers()
    users.value = adminStore.users
  } catch (error) {
    console.error('Error updating user status:', error)
  }
}

const updateUserRole = async ({ userId, role }) => {
  try {
    await adminStore.updateUserRole(userId, role)
    await adminStore.fetchUsers()
    users.value = adminStore.users
  } catch (error) {
    console.error('Error updating user role:', error)
  }
}

const deleteUser = async (userId) => {
  try {
    // Mock delete user functionality
    users.value = users.value.filter(user => user.id !== userId)
  } catch (error) {
    console.error('Error deleting user:', error)
  }
}

// Lifecycle
onMounted(async () => {
  try {
    // Load all necessary data
    await Promise.all([
      artworkStore.fetchArtworks(),
      adminStore.fetchOrders(),
      adminStore.fetchUsers(),
      adminStore.fetchAnalytics()
    ])
    
    artworks.value = artworkStore.artworks
    orders.value = adminStore.orders
    users.value = adminStore.users
  } catch (error) {
    console.error('Error loading admin data:', error)
  }
})
</script>

<style scoped>
.admin-dashboard {
  min-height: 100vh;
  background-color: #f9fafb;
}

/* Smooth transitions for sidebar */
.admin-dashboard .transform {
  transition: transform 0.3s ease-in-out;
}

/* Mobile responsive adjustments */
@media (max-width: 1024px) {
  .admin-dashboard .lg\:ml-64 {
    margin-left: 0;
  }
}

/* Modal animations */
.admin-dashboard .fixed.inset-0 {
  animation: fadeIn 0.15s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Custom scrollbar for main content */
.admin-dashboard main::-webkit-scrollbar {
  width: 6px;
}

.admin-dashboard main::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.admin-dashboard main::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.admin-dashboard main::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>