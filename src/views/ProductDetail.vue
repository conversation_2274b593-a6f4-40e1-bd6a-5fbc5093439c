<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center min-h-screen">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error || !artwork" class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <div class="text-red-600 mb-4">
          <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Artwork not found</h3>
        <p class="text-gray-600 mb-4">The artwork you're looking for doesn't exist or has been removed.</p>
        <RouterLink 
          to="/gallery"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Back to Gallery
        </RouterLink>
      </div>
    </div>

    <!-- Main Content -->
    <div v-else>
      <!-- Breadcrumb Navigation -->
      <nav class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <ol class="flex items-center space-x-2 text-sm">
            <li>
              <RouterLink to="/" class="text-gray-500 hover:text-gray-700">Home</RouterLink>
            </li>
            <li class="text-gray-400">/</li>
            <li>
              <RouterLink to="/gallery" class="text-gray-500 hover:text-gray-700">Gallery</RouterLink>
            </li>
            <li class="text-gray-400">/</li>
            <li class="text-gray-900 font-medium">{{ artwork.title }}</li>
          </ol>
        </div>
      </nav>

      <!-- Product Details -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="lg:grid lg:grid-cols-2 lg:gap-12">
          <!-- Image Gallery -->
          <div class="mb-8 lg:mb-0">
            <div class="aspect-square bg-white rounded-lg shadow-lg overflow-hidden">
              <img 
                :src="artwork.imageUrl" 
                :alt="artwork.title"
                class="w-full h-full object-cover"
                @error="handleImageError"
              />
            </div>
            
            <!-- Image Actions -->
            <div class="mt-4 flex justify-center space-x-4">
              <button 
                @click="openImageModal"
                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                View Full Size
              </button>
            </div>
          </div>

          <!-- Product Information -->
          <div>
            <div class="mb-6">
              <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ artwork.title }}</h1>
              <p class="text-xl text-gray-600 mb-4">by {{ artwork.artist }}</p>
              
              <!-- Price and Category -->
              <div class="flex items-center justify-between mb-6">
                <div class="text-3xl font-bold text-gray-900">
                  ${{ formatPrice(artwork.price) }}
                </div>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  {{ artwork.category }}
                </span>
              </div>
            </div>

            <!-- Description -->
            <div class="mb-8">
              <h3 class="text-lg font-medium text-gray-900 mb-3">Description</h3>
              <p class="text-gray-700 leading-relaxed">{{ artwork.description }}</p>
            </div>

            <!-- Specifications -->
            <div class="mb-8">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Specifications</h3>
              <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <dt class="text-sm font-medium text-gray-500">Medium</dt>
                  <dd class="mt-1 text-sm text-gray-900">{{ artwork.medium }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Style</dt>
                  <dd class="mt-1 text-sm text-gray-900">{{ artwork.style }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Category</dt>
                  <dd class="mt-1 text-sm text-gray-900">{{ artwork.category }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Artist</dt>
                  <dd class="mt-1 text-sm text-gray-900">{{ artwork.artist }}</dd>
                </div>
              </dl>
            </div>

            <!-- Actions -->
            <div class="space-y-4">
              <div class="flex space-x-4">
                <button 
                  @click="addToCart"
                  :disabled="isAddingToCart"
                  class="flex-1 bg-blue-600 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  {{ isAddingToCart ? 'Adding to Cart...' : 'Add to Cart' }}
                </button>
                
                <button 
                  @click="toggleWishlist"
                  class="px-6 py-3 border border-gray-300 rounded-md font-medium hover:bg-gray-50 transition-colors duration-200"
                  :class="{ 'text-red-600 border-red-300 bg-red-50': isInWishlist, 'text-gray-700': !isInWishlist }"
                >
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                  </svg>
                </button>
              </div>

              <!-- Additional Info -->
              <div class="text-sm text-gray-600 space-y-2">
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Authentic artwork with certificate of authenticity
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  Secure cryptocurrency payment accepted
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                  Professional packaging and insured shipping
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Related Artworks -->
        <div v-if="relatedArtworks.length > 0" class="mt-16">
          <h2 class="text-2xl font-bold text-gray-900 mb-8">Related Artworks</h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <ProductCard 
              v-for="relatedArtwork in relatedArtworks" 
              :key="relatedArtwork.id"
              :artwork="relatedArtwork"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Image Modal -->
    <div v-if="showImageModal" class="fixed inset-0 z-50 overflow-y-auto" @click="closeImageModal">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black bg-opacity-75 transition-opacity"></div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium text-gray-900">{{ artwork.title }}</h3>
              <button @click="closeImageModal" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <img 
              :src="artwork.imageUrl" 
              :alt="artwork.title"
              class="w-full h-auto max-h-96 object-contain"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, RouterLink } from 'vue-router'
import { useArtworkStore } from '../stores/artwork'
import { useCartStore } from '../stores/cart'
import { useWishlistStore } from '../stores/wishlist'
import ProductCard from '../components/ProductCard.vue'

const route = useRoute()
const artworkStore = useArtworkStore()
const cartStore = useCartStore()
const wishlistStore = useWishlistStore()

// Reactive state
const loading = ref(true)
const error = ref(null)
const isAddingToCart = ref(false)
const showImageModal = ref(false)

// Computed properties
const artwork = computed(() => {
  return artworkStore.getArtworkById(route.params.id)
})

const relatedArtworks = computed(() => {
  if (!artwork.value) return []
  return artworkStore.getRelatedArtworks(artwork.value, 4)
})

const isInWishlist = computed(() => {
  if (!artwork.value) return false
  return wishlistStore.isInWishlist(artwork.value.id)
})

// Methods
const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US').format(price)
}

const handleImageError = (event) => {
  event.target.src = '/images/placeholder-art.jpg'
}

const addToCart = async () => {
  if (!artwork.value) return
  
  isAddingToCart.value = true
  try {
    await cartStore.addItem(artwork.value)
    // Show success notification (could be implemented with a toast system)
  } catch (error) {
    console.error('Failed to add item to cart:', error)
    // Show error notification
  } finally {
    isAddingToCart.value = false
  }
}

const toggleWishlist = () => {
  if (!artwork.value) return
  wishlistStore.toggleItem(artwork.value)
}

const openImageModal = () => {
  showImageModal.value = true
}

const closeImageModal = () => {
  showImageModal.value = false
}

// Initialize data
onMounted(async () => {
  try {
    if (artworkStore.artworks.length === 0) {
      await artworkStore.fetchArtworks()
    }
    
    if (!artwork.value) {
      error.value = 'Artwork not found'
    }
  } catch (err) {
    error.value = err.message
  } finally {
    loading.value = false
  }
})
</script>