<template>
  <div class="min-h-screen bg-white">
    <!-- Hero Section -->
    <div class="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      <div class="absolute inset-0 bg-black opacity-50"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
            Discover Contemporary
            <span class="block text-blue-400">Art Masterpieces</span>
          </h1>
          <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Explore our curated collection of contemporary artworks from world-renowned artists. 
            Own a piece of art history with secure cryptocurrency payments.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <RouterLink 
              to="/gallery" 
              class="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200"
            >
              Browse Gallery
              <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </RouterLink>
            <button 
              @click="scrollToFeatured"
              class="inline-flex items-center px-8 py-4 border border-white text-lg font-medium rounded-md text-white bg-transparent hover:bg-white hover:text-gray-900 transition-colors duration-200"
            >
              View Featured
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Section -->
    <div class="bg-gray-50 py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
          <div>
            <div class="text-3xl font-bold text-gray-900">{{ totalArtworks }}+</div>
            <div class="text-gray-600 mt-2">Curated Artworks</div>
          </div>
          <div>
            <div class="text-3xl font-bold text-gray-900">{{ uniqueArtists }}+</div>
            <div class="text-gray-600 mt-2">Renowned Artists</div>
          </div>
          <div>
            <div class="text-3xl font-bold text-gray-900">{{ uniqueStyles }}+</div>
            <div class="text-gray-600 mt-2">Art Styles</div>
          </div>
          <div>
            <div class="text-3xl font-bold text-gray-900">100%</div>
            <div class="text-gray-600 mt-2">Authenticated</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Featured Artworks Section -->
    <div ref="featuredSection" class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Artworks</h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our most coveted pieces from internationally acclaimed contemporary artists
          </p>
        </div>

        <div v-if="loading" class="flex justify-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>

        <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          <ProductCard 
            v-for="artwork in featuredArtworks" 
            :key="artwork.id"
            :artwork="artwork"
          />
        </div>

        <div class="text-center mt-12">
          <RouterLink 
            to="/gallery"
            class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
          >
            View All Artworks
            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </RouterLink>
        </div>
      </div>
    </div>

    <!-- New Arrivals Section -->
    <div class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">New Arrivals</h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Fresh additions to our collection from emerging and established artists
          </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          <ProductCard 
            v-for="artwork in newArrivals" 
            :key="artwork.id"
            :artwork="artwork"
          />
        </div>
      </div>
    </div>

    <!-- Artist Spotlight Section -->
    <div class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Artist Spotlight</h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Meet the visionary artists behind our contemporary masterpieces
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div 
            v-for="artist in featuredArtists" 
            :key="artist.name"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
          >
            <div class="aspect-square bg-gray-200 relative overflow-hidden">
              <img 
                :src="artist.artwork.imageUrl" 
                :alt="`Artwork by ${artist.name}`"
                class="w-full h-full object-cover"
              />
              <div class="absolute inset-0 bg-black bg-opacity-40 flex items-end">
                <div class="p-6 text-white">
                  <h3 class="text-xl font-bold mb-2">{{ artist.name }}</h3>
                  <p class="text-sm opacity-90">{{ artist.style }}</p>
                </div>
              </div>
            </div>
            <div class="p-6">
              <p class="text-gray-600 text-sm mb-4">{{ artist.description }}</p>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-500">{{ artist.artworkCount }} artworks</span>
                <RouterLink 
                  to="/gallery"
                  class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  View Works →
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Features Section -->
    <div class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Why Choose Our Gallery</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Authenticated Artworks</h3>
            <p class="text-gray-600">Every piece comes with a certificate of authenticity and detailed provenance</p>
          </div>
          
          <div class="text-center">
            <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Secure Payments</h3>
            <p class="text-gray-600">Safe and secure cryptocurrency transactions with blockchain verification</p>
          </div>
          
          <div class="text-center">
            <div class="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Global Shipping</h3>
            <p class="text-gray-600">Professional packaging and insured worldwide delivery to your doorstep</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Newsletter Section -->
    <div class="py-16 bg-gray-900">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-white mb-4">Stay Updated</h2>
        <p class="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
          Be the first to know about new arrivals, exclusive exhibitions, and special offers
        </p>
        <div class="max-w-md mx-auto">
          <div class="flex">
            <input 
              type="email" 
              placeholder="Enter your email"
              class="flex-1 px-4 py-3 rounded-l-md border-0 focus:ring-2 focus:ring-blue-500"
            />
            <button class="px-6 py-3 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 transition-colors duration-200">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { RouterLink } from 'vue-router'
import { useArtworkStore } from '../stores/artwork'
import ProductCard from '../components/ProductCard.vue'

const artworkStore = useArtworkStore()
const featuredSection = ref(null)

// Reactive state
const loading = ref(true)

// Computed properties
const featuredArtworks = computed(() => artworkStore.getFeaturedArtworks(6))
const newArrivals = computed(() => artworkStore.getNewArrivals(6))
const totalArtworks = computed(() => artworkStore.artworks.length)

const uniqueArtists = computed(() => {
  const artists = new Set(artworkStore.artworks.map(artwork => artwork.artist))
  return artists.size
})

const uniqueStyles = computed(() => {
  const styles = new Set(artworkStore.artworks.map(artwork => artwork.style))
  return styles.size
})

const featuredArtists = computed(() => {
  // Group artworks by artist and create artist profiles
  const artistMap = new Map()
  
  artworkStore.artworks.forEach(artwork => {
    if (!artistMap.has(artwork.artist)) {
      artistMap.set(artwork.artist, {
        name: artwork.artist,
        style: artwork.style,
        artworks: [],
        description: getArtistDescription(artwork.artist)
      })
    }
    artistMap.get(artwork.artist).artworks.push(artwork)
  })

  // Convert to array and get top 3 artists by artwork count
  return Array.from(artistMap.values())
    .sort((a, b) => b.artworks.length - a.artworks.length)
    .slice(0, 3)
    .map(artist => ({
      name: artist.name,
      style: artist.style,
      description: artist.description,
      artworkCount: artist.artworks.length,
      artwork: artist.artworks[0] // Use first artwork as representative image
    }))
})

// Methods
const getArtistDescription = (artistName) => {
  const descriptions = {
    'Andy Warhol': 'Leading figure in the pop art movement, known for exploring popular culture and consumerism.',
    'Jackson Pollock': 'Pioneer of abstract expressionism, famous for his innovative drip painting technique.',
    'Louise Bourgeois': 'Renowned sculptor exploring themes of femininity, family, and the unconscious.',
    'David Hockney': 'Contemporary artist known for vibrant paintings and innovative use of technology.',
    'Cecily Brown': 'Contemporary painter known for expressive abstract works with bold brushstrokes.',
    'Kara Walker': 'Contemporary artist addressing issues of race, gender, and identity through powerful silhouettes.',
    'Felix Gonzalez-Torres': 'Conceptual artist known for minimalist installations exploring love and loss.',
    'Njideka Akunyili Crosby': 'Contemporary artist exploring cultural identity through mixed media works.',
    'Banksy': 'Anonymous street artist known for provocative and politically charged artworks.',
    'Willem de Kooning': 'Abstract expressionist painter known for his gestural and emotionally charged works.'
  }
  
  return descriptions[artistName] || 'Renowned contemporary artist with a distinctive artistic vision.'
}

const scrollToFeatured = () => {
  featuredSection.value?.scrollIntoView({ 
    behavior: 'smooth',
    block: 'start'
  })
}

// Initialize data
onMounted(async () => {
  try {
    await artworkStore.fetchArtworks()
  } catch (error) {
    console.error('Failed to load artworks:', error)
  } finally {
    loading.value = false
  }
})
</script>