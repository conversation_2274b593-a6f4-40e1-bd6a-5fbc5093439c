<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">My Orders</h1>
      <p class="mt-2 text-gray-600">Track and manage your artwork purchases</p>
    </div>

    <!-- Orders List -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
    </div>

    <div v-else-if="orders.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No orders yet</h3>
      <p class="mt-1 text-sm text-gray-500">Start browsing our gallery to make your first purchase.</p>
      <div class="mt-6">
        <RouterLink
          to="/gallery"
          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Browse Gallery
        </RouterLink>
      </div>
    </div>

    <div v-else class="space-y-6">
      <div
        v-for="order in orders"
        :key="order.id"
        class="bg-white shadow rounded-lg overflow-hidden"
      >
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-medium text-gray-900">
                Order #{{ order.id }}
              </h3>
              <p class="text-sm text-gray-500">
                Placed on {{ formatDate(order.createdAt) }}
              </p>
            </div>
            <div class="flex items-center space-x-4">
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="getStatusClass(order.status)"
              >
                {{ getStatusText(order.status) }}
              </span>
              <span class="text-lg font-semibold text-gray-900">
                ${{ order.totalAmount.toLocaleString() }}
              </span>
            </div>
          </div>
        </div>

        <div class="px-6 py-4">
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0">
              <img
                :src="getArtworkImage(order.artworkId)"
                :alt="getArtworkTitle(order.artworkId)"
                class="h-20 w-20 object-cover rounded-lg"
              />
            </div>
            <div class="flex-1 min-w-0">
              <h4 class="text-base font-medium text-gray-900 truncate">
                {{ getArtworkTitle(order.artworkId) }}
              </h4>
              <p class="text-sm text-gray-500">
                by {{ getArtworkArtist(order.artworkId) }}
              </p>
              <p class="text-sm text-gray-500">
                Payment: {{ formatPaymentMethod(order.paymentMethod) }}
              </p>
            </div>
            <div class="flex-shrink-0">
              <button
                @click="viewOrderDetails(order)"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                View Details
              </button>
            </div>
          </div>
        </div>

        <!-- Order Timeline -->
        <div v-if="order.status !== 'pending'" class="px-6 py-4 bg-gray-50">
          <div class="flow-root">
            <ul class="-mb-8">
              <li v-for="(event, eventIdx) in getOrderTimeline(order)" :key="eventIdx">
                <div class="relative pb-8">
                  <span
                    v-if="eventIdx !== getOrderTimeline(order).length - 1"
                    class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                  ></span>
                  <div class="relative flex space-x-3">
                    <div>
                      <span
                        class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white"
                        :class="event.completed ? 'bg-green-500' : 'bg-gray-400'"
                      >
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                      </span>
                    </div>
                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p class="text-sm text-gray-500">
                          {{ event.title }}
                        </p>
                      </div>
                      <div class="text-right text-sm whitespace-nowrap text-gray-500">
                        {{ event.date }}
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Order Details Modal -->
    <div
      v-if="selectedOrder"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="closeOrderDetails"
    >
      <div
        class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"
        @click.stop
      >
        <div class="mt-3">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              Order Details #{{ selectedOrder.id }}
            </h3>
            <button
              @click="closeOrderDetails"
              class="text-gray-400 hover:text-gray-600"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Order Date</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(selectedOrder.createdAt) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Status</label>
                <span
                  class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusClass(selectedOrder.status)"
                >
                  {{ getStatusText(selectedOrder.status) }}
                </span>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Payment Method</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatPaymentMethod(selectedOrder.paymentMethod) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Total Amount</label>
                <p class="mt-1 text-sm text-gray-900 font-semibold">${{ selectedOrder.totalAmount.toLocaleString() }}</p>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-500 mb-2">Artwork</label>
              <div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                <img
                  :src="getArtworkImage(selectedOrder.artworkId)"
                  :alt="getArtworkTitle(selectedOrder.artworkId)"
                  class="h-16 w-16 object-cover rounded-lg"
                />
                <div>
                  <h4 class="text-base font-medium text-gray-900">
                    {{ getArtworkTitle(selectedOrder.artworkId) }}
                  </h4>
                  <p class="text-sm text-gray-500">
                    by {{ getArtworkArtist(selectedOrder.artworkId) }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button
              @click="closeOrderDetails"
              class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Close
            </button>
            <button
              v-if="selectedOrder.status === 'pending'"
              @click="cancelOrder(selectedOrder)"
              class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              Cancel Order
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { RouterLink } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { mockOrders, mockArtworks } from '../services/mockData'

const authStore = useAuthStore()

const loading = ref(true)
const orders = ref([])
const selectedOrder = ref(null)

// Computed properties
const userOrders = computed(() => {
  return mockOrders.filter(order => order.userId === authStore.user?.id)
})

// Methods
const loadOrders = async () => {
  loading.value = true
  
  try {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    orders.value = userOrders.value.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  } catch (error) {
    console.error('Failed to load orders:', error)
  } finally {
    loading.value = false
  }
}

const getArtworkTitle = (artworkId) => {
  const artwork = mockArtworks.find(art => art.id === artworkId)
  return artwork?.title || 'Unknown Artwork'
}

const getArtworkArtist = (artworkId) => {
  const artwork = mockArtworks.find(art => art.id === artworkId)
  return artwork?.artist || 'Unknown Artist'
}

const getArtworkImage = (artworkId) => {
  const artwork = mockArtworks.find(art => art.id === artworkId)
  return artwork?.image || '/images/placeholder-artwork.jpg'
}

const getStatusClass = (status) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    shipped: 'bg-purple-100 text-purple-800',
    delivered: 'bg-green-100 text-green-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status) => {
  const texts = {
    pending: 'Pending',
    processing: 'Processing',
    shipped: 'Shipped',
    delivered: 'Delivered',
    completed: 'Completed',
    cancelled: 'Cancelled'
  }
  return texts[status] || 'Unknown'
}

const formatPaymentMethod = (method) => {
  const methods = {
    cryptocurrency: 'Cryptocurrency',
    credit_card: 'Credit Card',
    bank_transfer: 'Bank Transfer'
  }
  return methods[method] || method
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getOrderTimeline = (order) => {
  const timeline = [
    {
      title: 'Order placed',
      date: formatDate(order.createdAt),
      completed: true
    }
  ]

  if (order.status === 'processing' || order.status === 'shipped' || order.status === 'delivered' || order.status === 'completed') {
    timeline.push({
      title: 'Payment confirmed',
      date: formatDate(order.createdAt),
      completed: true
    })
  }

  if (order.status === 'shipped' || order.status === 'delivered' || order.status === 'completed') {
    timeline.push({
      title: 'Artwork shipped',
      date: formatDate(order.updatedAt),
      completed: true
    })
  }

  if (order.status === 'delivered' || order.status === 'completed') {
    timeline.push({
      title: 'Artwork delivered',
      date: formatDate(order.updatedAt),
      completed: true
    })
  }

  return timeline
}

const viewOrderDetails = (order) => {
  selectedOrder.value = order
}

const closeOrderDetails = () => {
  selectedOrder.value = null
}

const cancelOrder = async (order) => {
  if (confirm('Are you sure you want to cancel this order?')) {
    try {
      // In a real app, you would make an API call to cancel the order
      const orderIndex = orders.value.findIndex(o => o.id === order.id)
      if (orderIndex !== -1) {
        orders.value[orderIndex].status = 'cancelled'
        orders.value[orderIndex].updatedAt = new Date().toISOString()
      }
      
      selectedOrder.value = null
      alert('Order cancelled successfully')
    } catch (error) {
      alert('Failed to cancel order. Please try again.')
    }
  }
}

// Initialize
onMounted(() => {
  loadOrders()
})
</script>