<template>
  <div class="min-h-screen bg-white px-4 py-16 sm:px-6 sm:py-24 md:grid md:place-items-center lg:px-8">
    <div class="max-w-max mx-auto">
      <main class="sm:flex">
        <p class="text-4xl font-extrabold text-primary-600 sm:text-5xl">404</p>
        <div class="sm:ml-6">
          <div class="sm:border-l sm:border-gray-200 sm:pl-6">
            <h1 class="text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl">
              Page not found
            </h1>
            <p class="mt-1 text-base text-gray-500">
              Sorry, we couldn't find the page you're looking for.
            </p>
          </div>
          <div class="mt-10 flex space-x-3 sm:border-l sm:border-transparent sm:pl-6">
            <RouterLink
              to="/"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Go back home
            </RouterLink>
            <RouterLink
              to="/gallery"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Browse Gallery
            </RouterLink>
          </div>
        </div>
      </main>
      
      <!-- Decorative artwork grid -->
      <div class="mt-16">
        <h2 class="text-lg font-medium text-gray-900 mb-6 text-center">
          While you're here, check out some featured artworks
        </h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="artwork in featuredArtworks"
            :key="artwork.id"
            class="group relative bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
          >
            <div class="aspect-w-4 aspect-h-3 rounded-t-lg overflow-hidden">
              <img
                :src="artwork.image"
                :alt="artwork.title"
                class="w-full h-48 object-cover group-hover:opacity-75 transition-opacity duration-200"
              />
            </div>
            <div class="p-4">
              <h3 class="text-sm font-medium text-gray-900 truncate">
                {{ artwork.title }}
              </h3>
              <p class="text-sm text-gray-500">{{ artwork.artist }}</p>
              <p class="text-sm font-semibold text-gray-900 mt-1">
                ${{ artwork.price.toLocaleString() }}
              </p>
            </div>
            <RouterLink
              to="/gallery"
              class="absolute inset-0 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <span class="sr-only">View {{ artwork.title }}</span>
            </RouterLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { RouterLink } from 'vue-router'
import { mockArtworks } from '../services/mockData'

// Get a few featured artworks to display
const featuredArtworks = computed(() => {
  return mockArtworks.slice(0, 3)
})
</script>