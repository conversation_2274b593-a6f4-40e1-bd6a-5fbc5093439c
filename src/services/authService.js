import { mockUsers } from './mockData'

// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// Generate mock JWT token with payload
const generateMockToken = (user) => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }))
  const payload = btoa(JSON.stringify({
    sub: user.id,
    email: user.email,
    role: user.role,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  }))
  const signature = btoa('mock-signature')
  
  return `${header}.${payload}.${signature}`
}

// Validate email format
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Validate password strength
const isValidPassword = (password) => {
  const minLength = 8
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[a-z]/.test(password)
  const hasNumbers = /\d/.test(password)
  
  return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers
}

export const authService = {
  async login(email, password) {
    await delay(800) // Simulate network delay
    
    // Validate input
    if (!email || !password) {
      throw new Error('Email and password are required')
    }
    
    if (!isValidEmail(email)) {
      throw new Error('Invalid email format')
    }
    
    const user = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase())
    
    if (!user) {
      throw new Error('Invalid email or password')
    }
    
    if (user.password !== password) {
      throw new Error('Invalid email or password')
    }
    
    // Return user without password
    const { password: _, ...userWithoutPassword } = user
    
    return {
      user: userWithoutPassword,
      token: generateMockToken(user),
      message: 'Login successful'
    }
  },

  async register(userData) {
    await delay(1000)
    
    // Validate required fields
    if (!userData.email || !userData.password || !userData.name) {
      throw new Error('Email, password, and name are required')
    }
    
    // Validate email format
    if (!isValidEmail(userData.email)) {
      throw new Error('Invalid email format')
    }
    
    // Validate password strength
    if (!isValidPassword(userData.password)) {
      throw new Error('Password must be at least 8 characters long and contain uppercase, lowercase, and numbers')
    }
    
    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email.toLowerCase() === userData.email.toLowerCase())
    if (existingUser) {
      throw new Error('An account with this email already exists')
    }
    
    // Validate name length
    if (userData.name.trim().length < 2) {
      throw new Error('Name must be at least 2 characters long')
    }
    
    const newUser = {
      id: Date.now().toString(),
      email: userData.email.toLowerCase(),
      password: userData.password,
      name: userData.name.trim(),
      role: userData.role || 'customer', // Default to customer role
      avatar: userData.avatar || null,
      phone: userData.phone || null,
      address: userData.address || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    mockUsers.push(newUser)
    
    // Return user without password
    const { password: _, ...userWithoutPassword } = newUser
    
    return {
      user: userWithoutPassword,
      token: generateMockToken(newUser),
      message: 'Registration successful'
    }
  },

  async logout() {
    await delay(200)
    return { message: 'Logout successful' }
  },

  async validateToken(token) {
    await delay(300)
    
    if (!token) {
      return { valid: false, error: 'No token provided' }
    }
    
    try {
      // Parse mock JWT token
      const parts = token.split('.')
      if (parts.length !== 3) {
        return { valid: false, error: 'Invalid token format' }
      }
      
      const payload = JSON.parse(atob(parts[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      
      if (payload.exp && payload.exp < currentTime) {
        return { valid: false, error: 'Token expired' }
      }
      
      // Find user to ensure they still exist
      const user = mockUsers.find(u => u.id === payload.sub)
      if (!user) {
        return { valid: false, error: 'User not found' }
      }
      
      return { 
        valid: true, 
        user: { 
          id: user.id, 
          email: user.email, 
          name: user.name, 
          role: user.role 
        } 
      }
    } catch (error) {
      return { valid: false, error: 'Invalid token' }
    }
  },

  async getCurrentUser(token) {
    return await this.validateToken(token)
  },

  async updateProfile(userId, userData) {
    await delay(500)
    
    const userIndex = mockUsers.findIndex(u => u.id === userId)
    if (userIndex === -1) {
      throw new Error('User not found')
    }
    
    // Validate email if being updated
    if (userData.email && !isValidEmail(userData.email)) {
      throw new Error('Invalid email format')
    }
    
    // Check if email is already taken by another user
    if (userData.email) {
      const existingUser = mockUsers.find(u => 
        u.email.toLowerCase() === userData.email.toLowerCase() && u.id !== userId
      )
      if (existingUser) {
        throw new Error('Email is already taken')
      }
    }
    
    // Validate name if being updated
    if (userData.name && userData.name.trim().length < 2) {
      throw new Error('Name must be at least 2 characters long')
    }
    
    // Validate password if being updated
    if (userData.password && !isValidPassword(userData.password)) {
      throw new Error('Password must be at least 8 characters long and contain uppercase, lowercase, and numbers')
    }
    
    const updatedUser = {
      ...mockUsers[userIndex],
      ...userData,
      email: userData.email ? userData.email.toLowerCase() : mockUsers[userIndex].email,
      name: userData.name ? userData.name.trim() : mockUsers[userIndex].name,
      updatedAt: new Date().toISOString()
    }
    
    mockUsers[userIndex] = updatedUser
    
    // Return user without password
    const { password: _, ...userWithoutPassword } = updatedUser
    return userWithoutPassword
  },

  async changePassword(userId, currentPassword, newPassword) {
    await delay(500)
    
    const user = mockUsers.find(u => u.id === userId)
    if (!user) {
      throw new Error('User not found')
    }
    
    if (user.password !== currentPassword) {
      throw new Error('Current password is incorrect')
    }
    
    if (!isValidPassword(newPassword)) {
      throw new Error('New password must be at least 8 characters long and contain uppercase, lowercase, and numbers')
    }
    
    if (currentPassword === newPassword) {
      throw new Error('New password must be different from current password')
    }
    
    const userIndex = mockUsers.findIndex(u => u.id === userId)
    mockUsers[userIndex].password = newPassword
    mockUsers[userIndex].updatedAt = new Date().toISOString()
    
    return { message: 'Password changed successfully' }
  },

  async resetPassword(email) {
    await delay(800)
    
    if (!isValidEmail(email)) {
      throw new Error('Invalid email format')
    }
    
    const user = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase())
    if (!user) {
      // Don't reveal if email exists for security
      return { message: 'If an account with this email exists, a password reset link has been sent' }
    }
    
    // In a real app, you would send an email with a reset token
    return { message: 'If an account with this email exists, a password reset link has been sent' }
  }
}