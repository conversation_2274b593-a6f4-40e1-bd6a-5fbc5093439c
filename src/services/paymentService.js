import NOWPaymentsApi from '@nowpaymentsio/nowpayments-api-js'

class PaymentService {
  constructor() {
    // Initialize NOWPayments API client
    // Using sandbox mode for development - replace with production keys for live environment
    this.apiKey = process.env.VITE_NOWPAYMENTS_API_KEY || 'sandbox-api-key'
    this.sandboxMode = process.env.NODE_ENV !== 'production'
    
    this.nowPayments = new NOWPaymentsApi({
      apiKey: this.apiKey,
      sandbox: this.sandboxMode
    })
    
    // Supported cryptocurrencies for the art store
    this.supportedCurrencies = [
      { symbol: 'btc', name: 'Bitcoin', icon: '₿' },
      { symbol: 'eth', name: 'Ethereum', icon: 'Ξ' },
      { symbol: 'ltc', name: '<PERSON><PERSON>oin', icon: 'Ł' },
      { symbol: 'usdt', name: 'Tether USD', icon: '₮' },
      { symbol: 'usdc', name: 'USD Coin', icon: '$' }
    ]
  }

  /**
   * Get available cryptocurrencies for payment
   * @returns {Promise<Array>} List of available currencies
   */
  async getAvailableCurrencies() {
    try {
      const currencies = await this.nowPayments.getCurrencies()
      
      // Filter to only show currencies we support in our UI
      const filteredCurrencies = currencies.currencies.filter(currency => 
        this.supportedCurrencies.some(supported => supported.symbol === currency.toLowerCase())
      )
      
      // Enhance with our UI data
      return filteredCurrencies.map(currency => {
        const uiData = this.supportedCurrencies.find(supported => 
          supported.symbol === currency.toLowerCase()
        )
        return {
          symbol: currency.toLowerCase(),
          name: uiData?.name || currency,
          icon: uiData?.icon || '₿',
          available: true
        }
      })
    } catch (error) {
      console.error('Error fetching available currencies:', error)
      // Return default supported currencies if API fails
      return this.supportedCurrencies.map(currency => ({
        ...currency,
        available: false
      }))
    }
  }

  /**
   * Get estimated payment amount in cryptocurrency
   * @param {number} usdAmount - Amount in USD
   * @param {string} currency - Target cryptocurrency symbol
   * @returns {Promise<Object>} Estimated amount and exchange rate
   */
  async getEstimatedAmount(usdAmount, currency) {
    try {
      const estimate = await this.nowPayments.getEstimatePrice({
        amount: usdAmount,
        currency_from: 'usd',
        currency_to: currency.toLowerCase()
      })
      
      return {
        estimatedAmount: estimate.estimated_amount,
        currency: currency.toLowerCase(),
        exchangeRate: estimate.estimated_amount / usdAmount,
        validUntil: new Date(Date.now() + 15 * 60 * 1000) // 15 minutes validity
      }
    } catch (error) {
      console.error('Error getting estimated amount:', error)
      throw new Error(`Failed to get price estimate for ${currency}`)
    }
  }

  /**
   * Create a new payment
   * @param {Object} paymentData - Payment information
   * @returns {Promise<Object>} Payment details including address and amount
   */
  async createPayment(paymentData) {
    try {
      const {
        amount,
        currency,
        orderId,
        customerEmail,
        description,
        callbackUrl
      } = paymentData

      const payment = await this.nowPayments.createPayment({
        price_amount: amount,
        price_currency: 'usd',
        pay_currency: currency.toLowerCase(),
        order_id: orderId,
        order_description: description || `Art Store Order #${orderId}`,
        ipn_callback_url: callbackUrl,
        success_url: `${window.location.origin}/order-confirmation/${orderId}`,
        cancel_url: `${window.location.origin}/cart`
      })

      return {
        paymentId: payment.payment_id,
        paymentStatus: payment.payment_status,
        payAddress: payment.pay_address,
        payAmount: payment.pay_amount,
        payCurrency: payment.pay_currency,
        priceAmount: payment.price_amount,
        priceCurrency: payment.price_currency,
        orderId: payment.order_id,
        paymentUrl: payment.invoice_url,
        createdAt: payment.created_at,
        updatedAt: payment.updated_at,
        expiresAt: new Date(Date.now() + 60 * 60 * 1000) // 1 hour expiry
      }
    } catch (error) {
      console.error('Error creating payment:', error)
      throw new Error('Failed to create cryptocurrency payment')
    }
  }

  /**
   * Get payment status
   * @param {string} paymentId - Payment ID from NOWPayments
   * @returns {Promise<Object>} Current payment status
   */
  async getPaymentStatus(paymentId) {
    try {
      const status = await this.nowPayments.getPaymentStatus(paymentId)
      
      return {
        paymentId: status.payment_id,
        paymentStatus: status.payment_status,
        payAddress: status.pay_address,
        payAmount: status.pay_amount,
        payCurrency: status.pay_currency,
        priceAmount: status.price_amount,
        priceCurrency: status.price_currency,
        orderId: status.order_id,
        actuallyPaid: status.actually_paid,
        createdAt: status.created_at,
        updatedAt: status.updated_at
      }
    } catch (error) {
      console.error('Error getting payment status:', error)
      throw new Error('Failed to get payment status')
    }
  }

  /**
   * Validate webhook signature (for production use)
   * @param {string} payload - Webhook payload
   * @param {string} signature - Webhook signature
   * @returns {boolean} Whether signature is valid
   */
  validateWebhookSignature(payload, signature) {
    // In production, implement proper signature validation
    // This is a placeholder for the webhook validation logic
    if (this.sandboxMode) {
      return true // Skip validation in sandbox mode
    }
    
    // Implement HMAC signature validation here
    return false
  }

  /**
   * Process webhook notification
   * @param {Object} webhookData - Webhook payload from NOWPayments
   * @returns {Object} Processed payment update
   */
  processWebhookNotification(webhookData) {
    return {
      paymentId: webhookData.payment_id,
      orderId: webhookData.order_id,
      paymentStatus: webhookData.payment_status,
      actuallyPaid: webhookData.actually_paid,
      payAmount: webhookData.pay_amount,
      payCurrency: webhookData.pay_currency,
      priceAmount: webhookData.price_amount,
      priceCurrency: webhookData.price_currency,
      updatedAt: webhookData.updated_at || new Date().toISOString()
    }
  }

  /**
   * Generate QR code data for payment address
   * @param {string} address - Cryptocurrency address
   * @param {number} amount - Payment amount
   * @param {string} currency - Currency symbol
   * @returns {string} QR code data string
   */
  generateQRCodeData(address, amount, currency) {
    // Generate appropriate QR code format based on currency
    switch (currency.toLowerCase()) {
      case 'btc':
        return `bitcoin:${address}?amount=${amount}`
      case 'eth':
      case 'usdt':
      case 'usdc':
        return `ethereum:${address}?value=${amount}`
      case 'ltc':
        return `litecoin:${address}?amount=${amount}`
      default:
        return address
    }
  }

  /**
   * Check if payment is expired
   * @param {Date} expiresAt - Payment expiration time
   * @returns {boolean} Whether payment has expired
   */
  isPaymentExpired(expiresAt) {
    return new Date() > new Date(expiresAt)
  }

  /**
   * Format cryptocurrency amount for display
   * @param {number} amount - Amount to format
   * @param {string} currency - Currency symbol
   * @returns {string} Formatted amount string
   */
  formatCryptoAmount(amount, currency) {
    const decimals = ['btc', 'eth', 'ltc'].includes(currency.toLowerCase()) ? 8 : 6
    return parseFloat(amount).toFixed(decimals)
  }
}

// Export singleton instance
export default new PaymentService()