// Mock data based on real contemporary artists and artworks
export const mockArtworks = [
  {
    id: '1',
    title: "<PERSON>'s Soup Cans",
    artist: '<PERSON>',
    category: 'Pop Art',
    medium: 'Acrylic on Canvas',
    price: 8500,
    imageUrl: 'https://www.moma.org/media/W1siZiIsIjMxODI0MiJdLFsicCIsImNvbnZlcnQiLCItcXVhbGl0eSA5MCAtcmVzaXplIDIwMDB4MTQ0MFx1MDAzZSJdXQ.jpg?sha=f1e923ce509ba9e6',
    description: 'Iconic pop art piece featuring Campbell soup cans, representing consumer culture.',
    style: 'Pop Art',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    title: 'Autumn Rhythm',
    artist: '<PERSON>',
    category: 'Abstract Art',
    medium: 'Oil on Canvas',
    price: 12000,
    imageUrl: 'https://publicdelivery.org/wp-content/uploads/2021/02/<PERSON>-<PERSON><PERSON>-Autumn-Rhythm-Number-30-1950-enamel-on-canvas-266.7-x-525.8-cm-8-ft.-9-in.-x-17-ft.-3-in.-scaled.jpg',
    description: 'Dynamic abstract expressionist painting with characteristic drip technique.',
    style: 'Abstract Expressionism',
    createdAt: '2024-01-16T11:30:00Z',
    updatedAt: '2024-01-16T11:30:00Z'
  },
  {
    id: '3',
    title: 'Maman',
    artist: 'Louise Bourgeois',
    category: 'Sculpture',
    medium: 'Bronze and Steel',
    price: 15000,
    imageUrl: 'https://www.guggenheim.org/wp-content/uploads/1999/01/GBM2001.1_ph_web-1.jpg',
    description: 'Monumental spider sculpture exploring themes of motherhood and protection.',
    style: 'Contemporary Sculpture',
    createdAt: '2024-01-17T14:20:00Z',
    updatedAt: '2024-01-17T14:20:00Z'
  },
  {
    id: '4',
    title: 'Pool with Two Figures',
    artist: 'David Hockney',
    category: 'Painting',
    medium: 'Acrylic on Canvas',
    price: 9200,
    imageUrl: 'https://www.christies.com/-/jssmedia/images/features/articles/2025/05/guide-updates/david-hockney-portrait-of-an-artist-pool-with-two-figures/hockney-portrait-of-an-artist-main-i.jpg?h=1650&iar=0&w=2640&rev=3353aee5041a4f7db8ede5792ce03ec6&hash=5b76c78cd9f40f412c2ed33d58de5540d817dff7',
    description: 'Vibrant painting showcasing Hockney\'s distinctive style and color palette.',
    style: 'Contemporary Realism',
    createdAt: '2024-01-18T09:15:00Z',
    updatedAt: '2024-01-18T09:15:00Z'
  },
  {
    id: '5',
    title: 'Untitled Portrait',
    artist: 'Cecily Brown',
    category: 'Abstract Art',
    medium: 'Oil on Canvas',
    price: 6800,
    imageUrl: 'https://media.newyorker.com/photos/64fa0a9b3ca898c36e5b3ca5/master/w_1600%2Cc_limit/arn-cecily%2520brown-untitled%2520(vanity).jpg',
    description: 'Expressive abstract work with bold brushstrokes and rich color combinations.',
    style: 'Abstract Expressionism',
    createdAt: '2024-01-19T16:45:00Z',
    updatedAt: '2024-01-19T16:45:00Z'
  },
  {
    id: '6',
    title: 'Silhouette Series',
    artist: 'Kara Walker',
    category: 'Mixed Media',
    medium: 'Cut Paper on Wall',
    price: 4500,
    imageUrl: 'https://www.moma.org/media/W1siZiIsIjIwMzA4OSJdLFsicCIsImNvbnZlcnQiLCItcXVhbGl0eSA5MCAtcmVzaXplIDIwMDB4MTQ0MFx1MDAzZSJdXQ.jpg?sha=95154b4410965a4f',
    description: 'Powerful silhouette work addressing themes of race and history.',
    style: 'Contemporary Social Commentary',
    createdAt: '2024-01-20T13:30:00Z',
    updatedAt: '2024-01-20T13:30:00Z'
  },
  {
    id: '7',
    title: 'Untitled (Perfect Lovers)',
    artist: 'Felix Gonzalez-Torres',
    category: 'Installation Art',
    medium: 'Synchronized Clocks',
    price: 7200,
    imageUrl: 'https://www.moma.org/media/W1siZiIsIjIxMDU3NiJdLFsicCIsImNvbnZlcnQiLCItcXVhbGl0eSA5MCAtcmVzaXplIDIwMDB4MTQ0MFx1MDAzZSJdXQ.jpg?sha=de52915faed01613',
    description: 'Conceptual installation exploring love, time, and mortality.',
    style: 'Conceptual Art',
    createdAt: '2024-01-21T12:00:00Z',
    updatedAt: '2024-01-21T12:00:00Z'
  },
  {
    id: '8',
    title: 'The Beautyful Ones',
    artist: 'Njideka Akunyili Crosby',
    category: 'Mixed Media',
    medium: 'Acrylic, Transfers, and Collage',
    price: 5600,
    imageUrl: 'https://static.frieze.com/files/inline-images/web-na24-mother-and-child-2016-a-cmyk.jpeg',
    description: 'Complex layered work exploring cultural identity and diaspora.',
    style: 'Contemporary Mixed Media',
    createdAt: '2024-01-22T15:20:00Z',
    updatedAt: '2024-01-22T15:20:00Z'
  },
  {
    id: '9',
    title: 'Girl with Balloon',
    artist: 'Banksy',
    category: 'Street Art',
    medium: 'Spray Paint on Canvas',
    price: 3200,
    imageUrl: 'https://d7hftxdivxxvm.cloudfront.net/?height=1200&quality=80&resize_to=fill&src=https%3A%2F%2Fartsy-media-uploads.s3.amazonaws.com%2FK8tYJw2l3ShtLjC9Gl9pMg%252FGirl%2Bwith%2BBalloon%2BMAG.jpeg&width=1200',
    description: 'Iconic street art piece with social and political commentary.',
    style: 'Street Art',
    createdAt: '2024-01-23T10:45:00Z',
    updatedAt: '2024-01-23T10:45:00Z'
  },
  {
    id: '10',
    title: 'Woman III',
    artist: 'Willem de Kooning',
    category: 'Abstract Art',
    medium: 'Oil on Canvas',
    price: 11500,
    imageUrl: 'https://static01.nyt.com/images/2006/11/18/arts/18pain.450.jpg?quality=75&auto=webp&disable=upscale',
    description: 'Bold abstract expressionist painting from the famous Woman series.',
    style: 'Abstract Expressionism',
    createdAt: '2024-01-24T14:10:00Z',
    updatedAt: '2024-01-24T14:10:00Z'
  }
]

export const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Admin User',
    role: 'admin',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'curator123',
    name: 'Art Curator',
    role: 'curator',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z'
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: 'customer123',
    name: 'Art Collector',
    role: 'customer',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z'
  }
]

export const mockOrders = [
  {
    id: '1',
    userId: '3',
    artworkId: '1',
    status: 'completed',
    totalAmount: 8500,
    paymentMethod: 'cryptocurrency',
    createdAt: '2024-01-25T10:30:00Z',
    updatedAt: '2024-01-25T10:30:00Z'
  },
  {
    id: '2',
    userId: '3',
    artworkId: '4',
    status: 'pending',
    totalAmount: 9200,
    paymentMethod: 'cryptocurrency',
    createdAt: '2024-01-26T14:15:00Z',
    updatedAt: '2024-01-26T14:15:00Z'
  }
]