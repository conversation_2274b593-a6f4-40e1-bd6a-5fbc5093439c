# NOWPayments API Configuration
# Get your API key from https://nowpayments.io/
# Use sandbox API key for development/testing
VITE_NOWPAYMENTS_API_KEY=your_nowpayments_api_key_here

# Environment Configuration
# Set to 'production' for live payments, 'development' for sandbox
NODE_ENV=development

# Application Configuration
VITE_APP_NAME=Contemporary Art Store
VITE_APP_URL=http://localhost:5173

# Production Configuration (for deployment)
# VITE_APP_URL=https://your-domain.com
# VITE_API_BASE_URL=https://api.your-domain.com

# Security Configuration
# VITE_ENABLE_ANALYTICS=true
# VITE_SENTRY_DSN=your_sentry_dsn_here

# Payment Configuration
# VITE_NOWPAYMENTS_SANDBOX=false (set to false for production)
VITE_NOWPAYMENTS_SANDBOX=true

# Feature Flags
VITE_ENABLE_REGISTRATION=true
VITE_ENABLE_GUEST_CHECKOUT=true
VITE_ENABLE_WISHLIST=true

# Admin Configuration
VITE_ADMIN_EMAIL=<EMAIL>