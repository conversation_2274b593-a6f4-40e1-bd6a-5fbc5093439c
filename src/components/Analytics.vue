<template>
  <div class="analytics">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h2 class="text-2xl font-bold text-gray-900">Analytics & Reports</h2>
        <p class="text-gray-600 mt-1">Sales metrics, insights, and performance data</p>
      </div>
      <div class="flex items-center space-x-3">
        <!-- Date Range Selector -->
        <select
          v-model="selectedDateRange"
          @change="updateAnalytics"
          class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="7">Last 7 days</option>
          <option value="30">Last 30 days</option>
          <option value="90">Last 3 months</option>
          <option value="365">Last year</option>
        </select>
        
        <!-- Export Report Button -->
        <button
          @click="exportReport"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          Export Report
        </button>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <!-- Total Revenue -->
      <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Revenue</p>
            <p class="text-2xl font-semibold text-gray-900">${{ analytics.totalRevenue.toLocaleString() }}</p>
            <p class="text-sm text-green-600">+12.5% from last period</p>
          </div>
        </div>
      </div>

      <!-- Total Orders -->
      <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Orders</p>
            <p class="text-2xl font-semibold text-gray-900">{{ analytics.totalOrders }}</p>
            <p class="text-sm text-blue-600">+8.2% from last period</p>
          </div>
        </div>
      </div>

      <!-- Average Order Value -->
      <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Avg. Order Value</p>
            <p class="text-2xl font-semibold text-gray-900">${{ averageOrderValue.toLocaleString() }}</p>
            <p class="text-sm text-purple-600">+5.1% from last period</p>
          </div>
        </div>
      </div>

      <!-- Conversion Rate -->
      <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"/>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Conversion Rate</p>
            <p class="text-2xl font-semibold text-gray-900">{{ conversionRate }}%</p>
            <p class="text-sm text-primary-600">+2.3% from last period</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Revenue Chart -->
      <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Revenue Trend</h3>
          <div class="flex items-center space-x-2">
            <button
              @click="revenueChartType = 'line'"
              :class="[
                'px-3 py-1 text-sm rounded-md',
                revenueChartType === 'line' ? 'bg-primary-100 text-primary-700' : 'text-gray-500 hover:text-gray-700'
              ]"
            >
              Line
            </button>
            <button
              @click="revenueChartType = 'bar'"
              :class="[
                'px-3 py-1 text-sm rounded-md',
                revenueChartType === 'bar' ? 'bg-primary-100 text-primary-700' : 'text-gray-500 hover:text-gray-700'
              ]"
            >
              Bar
            </button>
          </div>
        </div>
        
        <!-- Simple CSS-based Chart -->
        <div class="h-64">
          <div class="flex items-end justify-between h-full space-x-2">
            <div
              v-for="(data, index) in analytics.salesByMonth"
              :key="index"
              class="flex flex-col items-center flex-1"
            >
              <div
                :class="[
                  'w-full rounded-t-md transition-all duration-300',
                  revenueChartType === 'line' ? 'bg-primary-500' : 'bg-gradient-to-t from-primary-600 to-primary-400'
                ]"
                :style="{ height: `${(data.sales / maxSales) * 100}%` }"
              ></div>
              <div class="text-xs text-gray-500 mt-2">{{ data.month }}</div>
              <div class="text-xs font-medium text-gray-700">${{ (data.sales / 1000).toFixed(1) }}k</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Status Distribution -->
      <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Status Distribution</h3>
        
        <div class="space-y-4">
          <div
            v-for="status in orderStatusData"
            :key="status.name"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <div
                :class="[
                  'w-4 h-4 rounded-full',
                  status.color
                ]"
              ></div>
              <span class="text-sm font-medium text-gray-700 capitalize">{{ status.name }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">{{ status.count }}</span>
              <div class="w-20 bg-gray-200 rounded-full h-2">
                <div
                  :class="[
                    'h-2 rounded-full transition-all duration-300',
                    status.color.replace('bg-', 'bg-')
                  ]"
                  :style="{ width: `${status.percentage}%` }"
                ></div>
              </div>
              <span class="text-sm font-medium text-gray-900">{{ status.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Performing Artworks -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Top Artworks by Revenue -->
      <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Artworks by Revenue</h3>
        
        <div class="space-y-4">
          <div
            v-for="(artwork, index) in topArtworksByRevenue"
            :key="artwork.id"
            class="flex items-center space-x-4"
          >
            <div class="flex-shrink-0">
              <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-primary-100 text-primary-600 text-sm font-medium">
                {{ index + 1 }}
              </span>
            </div>
            <img
              :src="artwork.imageUrl"
              :alt="artwork.title"
              class="w-12 h-12 object-cover rounded-lg"
              @error="handleImageError"
            />
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">{{ artwork.title }}</p>
              <p class="text-sm text-gray-500">{{ artwork.artist }}</p>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-gray-900">${{ artwork.revenue.toLocaleString() }}</p>
              <p class="text-xs text-gray-500">{{ artwork.sales }} sales</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Customer Insights -->
      <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Insights</h3>
        
        <div class="space-y-6">
          <!-- New vs Returning Customers -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">Customer Type</h4>
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span class="text-sm text-gray-600">New ({{ customerInsights.newCustomers }})</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span class="text-sm text-gray-600">Returning ({{ customerInsights.returningCustomers }})</span>
              </div>
            </div>
            <div class="mt-2 flex h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                class="bg-blue-500"
                :style="{ width: `${(customerInsights.newCustomers / (customerInsights.newCustomers + customerInsights.returningCustomers)) * 100}%` }"
              ></div>
              <div
                class="bg-green-500"
                :style="{ width: `${(customerInsights.returningCustomers / (customerInsights.newCustomers + customerInsights.returningCustomers)) * 100}%` }"
              ></div>
            </div>
          </div>

          <!-- Geographic Distribution -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">Top Locations</h4>
            <div class="space-y-2">
              <div
                v-for="location in customerInsights.topLocations"
                :key="location.country"
                class="flex items-center justify-between"
              >
                <span class="text-sm text-gray-600">{{ location.country }}</span>
                <div class="flex items-center space-x-2">
                  <div class="w-16 bg-gray-200 rounded-full h-1.5">
                    <div
                      class="bg-primary-500 h-1.5 rounded-full"
                      :style="{ width: `${location.percentage}%` }"
                    ></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900">{{ location.percentage }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Customer Lifetime Value -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">Customer Metrics</h4>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-2xl font-semibold text-gray-900">${{ customerInsights.averageLifetimeValue.toLocaleString() }}</p>
                <p class="text-xs text-gray-500">Avg. Lifetime Value</p>
              </div>
              <div>
                <p class="text-2xl font-semibold text-gray-900">{{ customerInsights.averageOrdersPerCustomer }}</p>
                <p class="text-xs text-gray-500">Avg. Orders per Customer</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white p-6 rounded-lg shadow border border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
      
      <div class="space-y-4">
        <div
          v-for="activity in recentActivity"
          :key="activity.id"
          class="flex items-start space-x-3"
        >
          <div :class="[
            'flex-shrink-0 w-2 h-2 rounded-full mt-2',
            activity.type === 'order' ? 'bg-blue-500' :
            activity.type === 'user' ? 'bg-green-500' :
            activity.type === 'artwork' ? 'bg-purple-500' : 'bg-gray-500'
          ]"></div>
          <div class="flex-1">
            <p class="text-sm text-gray-900">{{ activity.description }}</p>
            <p class="text-xs text-gray-500">{{ formatTimeAgo(activity.timestamp) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAdminStore } from '../stores/admin'
import { useArtworkStore } from '../stores/artwork'

// Stores
const adminStore = useAdminStore()
const artworkStore = useArtworkStore()

// Reactive state
const selectedDateRange = ref(30)
const revenueChartType = ref('bar')

// Mock data for demonstration
const analytics = ref({
  totalRevenue: 125750,
  totalOrders: 47,
  salesByMonth: [
    { month: 'Jan', sales: 12500 },
    { month: 'Feb', sales: 15200 },
    { month: 'Mar', sales: 18700 },
    { month: 'Apr', sales: 16800 },
    { month: 'May', sales: 21300 },
    { month: 'Jun', sales: 19500 }
  ]
})

const orderStatusData = ref([
  { name: 'completed', count: 28, percentage: 60, color: 'bg-green-500' },
  { name: 'processing', count: 12, percentage: 25, color: 'bg-blue-500' },
  { name: 'pending', count: 5, percentage: 11, color: 'bg-yellow-500' },
  { name: 'cancelled', count: 2, percentage: 4, color: 'bg-red-500' }
])

const topArtworksByRevenue = ref([
  {
    id: '1',
    title: "Campbell's Soup Cans",
    artist: 'Andy Warhol',
    imageUrl: 'https://www.moma.org/media/W1siZiIsIjMxODI0MiJdLFsicCIsImNvbnZlcnQiLCItcXVhbGl0eSA5MCAtcmVzaXplIDIwMDB4MTQ0MFx1MDAzZSJdXQ.jpg?sha=f1e923ce509ba9e6',
    revenue: 25500,
    sales: 3
  },
  {
    id: '2',
    title: 'Autumn Rhythm',
    artist: 'Jackson Pollock',
    imageUrl: 'https://publicdelivery.org/wp-content/uploads/2021/02/Jackson-Pollock-Autumn-Rhythm-Number-30-1950-enamel-on-canvas-266.7-x-525.8-cm-8-ft.-9-in.-x-17-ft.-3-in.-scaled.jpg',
    revenue: 24000,
    sales: 2
  },
  {
    id: '3',
    title: 'Maman',
    artist: 'Louise Bourgeois',
    imageUrl: 'https://www.guggenheim.org/wp-content/uploads/1999/01/GBM2001.1_ph_web-1.jpg',
    revenue: 15000,
    sales: 1
  },
  {
    id: '4',
    title: 'Pool with Two Figures',
    artist: 'David Hockney',
    imageUrl: 'https://www.christies.com/-/jssmedia/images/features/articles/2025/05/guide-updates/david-hockney-portrait-of-an-artist-pool-with-two-figures/hockney-portrait-of-an-artist-main-i.jpg?h=1650&iar=0&w=2640&rev=3353aee5041a4f7db8ede5792ce03ec6&hash=5b76c78cd9f40f412c2ed33d58de5540d817dff7',
    revenue: 18400,
    sales: 2
  }
])

const customerInsights = ref({
  newCustomers: 32,
  returningCustomers: 15,
  averageLifetimeValue: 2850,
  averageOrdersPerCustomer: 1.8,
  topLocations: [
    { country: 'United States', percentage: 65 },
    { country: 'United Kingdom', percentage: 15 },
    { country: 'Canada', percentage: 12 },
    { country: 'Australia', percentage: 8 }
  ]
})

const recentActivity = ref([
  {
    id: 1,
    type: 'order',
    description: 'New order #47 for "Girl with Balloon" by <EMAIL>',
    timestamp: new Date(Date.now() - 5 * 60 * 1000)
  },
  {
    id: 2,
    type: 'user',
    description: 'New user registration: <EMAIL>',
    timestamp: new Date(Date.now() - 15 * 60 * 1000)
  },
  {
    id: 3,
    type: 'artwork',
    description: 'New artwork added: "Digital Dreams" by Modern Artist',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 4,
    type: 'order',
    description: 'Order #46 status updated to "shipped"',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)
  }
])

// Computed properties
const averageOrderValue = computed(() => {
  return analytics.value.totalOrders > 0 
    ? Math.round(analytics.value.totalRevenue / analytics.value.totalOrders)
    : 0
})

const conversionRate = computed(() => {
  // Mock conversion rate calculation
  return 3.2
})

const maxSales = computed(() => {
  return Math.max(...analytics.value.salesByMonth.map(data => data.sales))
})

// Methods
const updateAnalytics = () => {
  // Mock function to update analytics based on date range
  console.log('Updating analytics for', selectedDateRange.value, 'days')
}

const exportReport = () => {
  // Generate and download analytics report
  const reportData = {
    period: `Last ${selectedDateRange.value} days`,
    totalRevenue: analytics.value.totalRevenue,
    totalOrders: analytics.value.totalOrders,
    averageOrderValue: averageOrderValue.value,
    conversionRate: conversionRate.value,
    topArtworks: topArtworksByRevenue.value,
    customerInsights: customerInsights.value,
    generatedAt: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `analytics-report-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  window.URL.revokeObjectURL(url)
}

const handleImageError = (event) => {
  event.target.src = '/placeholder-image.jpg'
}

const formatTimeAgo = (timestamp) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now - timestamp) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
  return `${Math.floor(diffInMinutes / 1440)}d ago`
}

// Lifecycle
onMounted(async () => {
  // Load analytics data
  await adminStore.fetchAnalytics()
})
</script>

<style scoped>
.analytics {
  max-width: 100%;
}

/* Chart animations */
.analytics [style*="height"] {
  transition: height 0.3s ease-in-out;
}

/* Hover effects for interactive elements */
.analytics .bg-primary-100:hover {
  background-color: rgb(219 234 254);
}

/* Custom scrollbar for activity feed */
.space-y-4::-webkit-scrollbar {
  width: 4px;
}

.space-y-4::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.space-y-4::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.space-y-4::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>