<template>
  <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
    <div class="relative aspect-square overflow-hidden">
      <img 
        :src="artwork.imageUrl || '/images/placeholder-art.jpg'" 
        :alt="artwork.title"
        class="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
        @error="handleImageError"
      />
      <div class="absolute top-2 right-2">
        <button 
          @click="toggleWishlist"
          class="p-2 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white transition-colors duration-200"
          :class="{ 'text-red-500': isInWishlist, 'text-gray-400': !isInWishlist }"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
      <div class="absolute bottom-2 left-2">
        <span class="px-2 py-1 text-xs font-medium bg-black/70 text-white rounded-full backdrop-blur-sm">
          {{ artwork.category }}
        </span>
      </div>
    </div>
    
    <div class="p-4">
      <div class="mb-2">
        <h3 class="text-lg font-semibold text-gray-900 line-clamp-1">{{ artwork.title }}</h3>
        <p class="text-sm text-gray-600">by {{ artwork.artist }}</p>
      </div>
      
      <div class="mb-3">
        <p class="text-xs text-gray-500 mb-1">{{ artwork.medium }}</p>
        <p class="text-xs text-gray-400 line-clamp-2">{{ artwork.description }}</p>
      </div>
      
      <div class="flex justify-between items-center">
        <div class="flex flex-col">
          <span class="text-xl font-bold text-gray-900">${{ formatPrice(artwork.price) }}</span>
          <span class="text-xs text-gray-500">{{ artwork.style }}</span>
        </div>
        
        <div class="flex space-x-2">
          <button 
            @click="viewDetails"
            class="px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors duration-200"
          >
            View
          </button>
          <button 
            @click="addToCart"
            :disabled="isAddingToCart"
            class="px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            {{ isAddingToCart ? 'Adding...' : 'Add to Cart' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '../stores/cart'
import { useWishlistStore } from '../stores/wishlist'

const props = defineProps({
  artwork: {
    type: Object,
    required: true
  }
})

const router = useRouter()
const cartStore = useCartStore()
const wishlistStore = useWishlistStore()

const isAddingToCart = ref(false)

const isInWishlist = computed(() => {
  return wishlistStore.isInWishlist(props.artwork.id)
})

const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US').format(price)
}

const handleImageError = (event) => {
  event.target.src = '/images/placeholder-art.jpg'
}

const viewDetails = () => {
  router.push(`/artwork/${props.artwork.id}`)
}

const addToCart = async () => {
  isAddingToCart.value = true
  try {
    await cartStore.addItem(props.artwork)
    // Show success notification (could be implemented with a toast system)
  } catch (error) {
    console.error('Failed to add item to cart:', error)
    // Show error notification
  } finally {
    isAddingToCart.value = false
  }
}

const toggleWishlist = () => {
  if (isInWishlist.value) {
    wishlistStore.removeItem(props.artwork.id)
  } else {
    wishlistStore.addItem(props.artwork)
  }
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>