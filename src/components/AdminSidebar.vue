<template>
  <div class="admin-sidebar bg-gray-900 text-white w-64 min-h-screen flex flex-col">
    <!-- Logo/Brand -->
    <div class="p-6 border-b border-gray-700">
      <RouterLink to="/" class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
          <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"/>
            <path fill-rule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div>
          <h2 class="text-lg font-bold">Art Store</h2>
          <p class="text-xs text-gray-400">Admin Panel</p>
        </div>
      </RouterLink>
    </div>

    <!-- Navigation Menu -->
    <nav class="flex-1 px-4 py-6 space-y-2">
      <!-- Dashboard -->
      <div class="mb-6">
        <button
          @click="setActiveSection('dashboard')"
          :class="[
            'w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors',
            activeSection === 'dashboard' 
              ? 'bg-primary-600 text-white' 
              : 'text-gray-300 hover:bg-gray-800 hover:text-white'
          ]"
        >
          <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
          </svg>
          Dashboard
        </button>
      </div>

      <!-- Content Management -->
      <div class="mb-6">
        <h3 class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
          Content Management
        </h3>
        <div class="space-y-1">
          <button
            @click="setActiveSection('artworks')"
            :class="[
              'w-full flex items-center px-4 py-2 text-left rounded-lg transition-colors',
              activeSection === 'artworks' 
                ? 'bg-primary-600 text-white' 
                : 'text-gray-300 hover:bg-gray-800 hover:text-white'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
            </svg>
            Artworks
            <span v-if="artworkCount" class="ml-auto bg-gray-700 text-xs px-2 py-1 rounded-full">
              {{ artworkCount }}
            </span>
          </button>
          
          <button
            @click="setActiveSection('categories')"
            :class="[
              'w-full flex items-center px-4 py-2 text-left rounded-lg transition-colors',
              activeSection === 'categories' 
                ? 'bg-primary-600 text-white' 
                : 'text-gray-300 hover:bg-gray-800 hover:text-white'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"/>
            </svg>
            Categories
          </button>
          
          <button
            @click="setActiveSection('artists')"
            :class="[
              'w-full flex items-center px-4 py-2 text-left rounded-lg transition-colors',
              activeSection === 'artists' 
                ? 'bg-primary-600 text-white' 
                : 'text-gray-300 hover:bg-gray-800 hover:text-white'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
            </svg>
            Artists
          </button>
        </div>
      </div>

      <!-- Order Management -->
      <div class="mb-6">
        <h3 class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
          Order Management
        </h3>
        <div class="space-y-1">
          <button
            @click="setActiveSection('orders')"
            :class="[
              'w-full flex items-center px-4 py-2 text-left rounded-lg transition-colors',
              activeSection === 'orders' 
                ? 'bg-primary-600 text-white' 
                : 'text-gray-300 hover:bg-gray-800 hover:text-white'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM9 9a1 1 0 112 0v4a1 1 0 11-2 0V9z" clip-rule="evenodd"/>
            </svg>
            Orders
            <span v-if="pendingOrderCount > 0" class="ml-auto bg-red-600 text-xs px-2 py-1 rounded-full">
              {{ pendingOrderCount }}
            </span>
          </button>
        </div>
      </div>

      <!-- User Management -->
      <div class="mb-6" v-if="canManageUsers">
        <h3 class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
          User Management
        </h3>
        <div class="space-y-1">
          <button
            @click="setActiveSection('users')"
            :class="[
              'w-full flex items-center px-4 py-2 text-left rounded-lg transition-colors',
              activeSection === 'users' 
                ? 'bg-primary-600 text-white' 
                : 'text-gray-300 hover:bg-gray-800 hover:text-white'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
            </svg>
            Users
            <span v-if="userCount" class="ml-auto bg-gray-700 text-xs px-2 py-1 rounded-full">
              {{ userCount }}
            </span>
          </button>
        </div>
      </div>

      <!-- Analytics & Reports -->
      <div class="mb-6">
        <h3 class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
          Analytics & Reports
        </h3>
        <div class="space-y-1">
          <button
            @click="setActiveSection('analytics')"
            :class="[
              'w-full flex items-center px-4 py-2 text-left rounded-lg transition-colors',
              activeSection === 'analytics' 
                ? 'bg-primary-600 text-white' 
                : 'text-gray-300 hover:bg-gray-800 hover:text-white'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
            </svg>
            Analytics
          </button>
        </div>
      </div>

      <!-- System Settings -->
      <div class="mb-6" v-if="canManageSettings">
        <h3 class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
          System
        </h3>
        <div class="space-y-1">
          <button
            @click="setActiveSection('settings')"
            :class="[
              'w-full flex items-center px-4 py-2 text-left rounded-lg transition-colors',
              activeSection === 'settings' 
                ? 'bg-primary-600 text-white' 
                : 'text-gray-300 hover:bg-gray-800 hover:text-white'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
            </svg>
            Settings
          </button>
        </div>
      </div>
    </nav>

    <!-- User Info -->
    <div class="p-4 border-t border-gray-700">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
          <span class="text-sm font-medium text-white">
            {{ userInitials }}
          </span>
        </div>
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-white truncate">{{ userName }}</p>
          <p class="text-xs text-gray-400 capitalize">{{ userRole }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { RouterLink } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useArtworkStore } from '../stores/artwork'
import { useAdminStore } from '../stores/admin'

// Props
const props = defineProps({
  activeSection: {
    type: String,
    default: 'dashboard'
  }
})

// Emits
const emit = defineEmits(['section-changed'])

// Stores
const authStore = useAuthStore()
const artworkStore = useArtworkStore()
const adminStore = useAdminStore()

// Computed properties
const userName = computed(() => authStore.user?.name || 'Admin User')
const userRole = computed(() => authStore.user?.role || 'admin')
const userInitials = computed(() => {
  const name = userName.value
  return name.split(' ').map(n => n[0]).join('').toUpperCase()
})

const canManageUsers = computed(() => authStore.hasPermission('manage_users'))
const canManageSettings = computed(() => authStore.isAdmin)

const artworkCount = computed(() => artworkStore.artworks?.length || 0)
const userCount = computed(() => adminStore.users?.length || 0)
const pendingOrderCount = computed(() => adminStore.pendingOrders?.length || 0)

// Methods
const setActiveSection = (section) => {
  emit('section-changed', section)
}
</script>

<style scoped>
.admin-sidebar {
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for sidebar */
.admin-sidebar::-webkit-scrollbar {
  width: 4px;
}

.admin-sidebar::-webkit-scrollbar-track {
  background: #374151;
}

.admin-sidebar::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 2px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
</style>