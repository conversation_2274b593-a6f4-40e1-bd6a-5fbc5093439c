<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="mb-6">
      <h3 class="text-lg font-medium text-gray-900 mb-2">Select Payment Method</h3>
      <p class="text-sm text-gray-600">Choose your preferred cryptocurrency for payment</p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-3 text-gray-600">Loading payment methods...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Payment Method Error</h3>
          <p class="mt-1 text-sm text-red-700">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Payment Methods -->
    <div v-else class="space-y-4">
      <!-- Cryptocurrency Payment Option -->
      <div class="border border-gray-200 rounded-lg p-4">
        <div class="flex items-center mb-4">
          <input
            id="crypto-payment"
            v-model="paymentMethod"
            type="radio"
            value="crypto"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
          />
          <label for="crypto-payment" class="ml-3 flex items-center cursor-pointer">
            <span class="text-base font-medium text-gray-900">Cryptocurrency Payment</span>
            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Secure
            </span>
          </label>
        </div>

        <!-- Cryptocurrency Selection -->
        <div v-if="paymentMethod === 'crypto'" class="ml-7 space-y-3">
          <p class="text-sm text-gray-600 mb-3">Select your preferred cryptocurrency:</p>
          
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div
              v-for="currency in availableCurrencies"
              :key="currency.symbol"
              @click="selectCurrency(currency)"
              :class="[
                'relative rounded-lg border p-4 cursor-pointer transition-all duration-200',
                selectedCurrency?.symbol === currency.symbol
                  ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-500'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              ]"
            >
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-bold">
                    {{ currency.icon }}
                  </div>
                </div>
                <div class="ml-3 flex-1">
                  <div class="text-sm font-medium text-gray-900">{{ currency.name }}</div>
                  <div class="text-xs text-gray-500 uppercase">{{ currency.symbol }}</div>
                </div>
                <div v-if="selectedCurrency?.symbol === currency.symbol" class="flex-shrink-0">
                  <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
              
              <!-- Availability indicator -->
              <div v-if="!currency.available" class="absolute top-2 right-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  Limited
                </span>
              </div>
            </div>
          </div>

          <!-- Payment Estimate -->
          <div v-if="selectedCurrency && totalAmount" class="mt-4 p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700">Payment Amount:</span>
              <button
                @click="refreshEstimate"
                :disabled="estimateLoading"
                class="text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
              >
                {{ estimateLoading ? 'Updating...' : 'Refresh' }}
              </button>
            </div>
            
            <div v-if="paymentEstimate" class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">USD Amount:</span>
                <span class="text-sm font-medium text-gray-900">${{ formatPrice(totalAmount) }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">{{ selectedCurrency.name }} Amount:</span>
                <span class="text-sm font-medium text-gray-900">
                  {{ paymentEstimate.estimatedAmount }} {{ selectedCurrency.symbol.toUpperCase() }}
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-xs text-gray-500">Exchange Rate:</span>
                <span class="text-xs text-gray-500">
                  1 USD = {{ paymentEstimate.exchangeRate.toFixed(8) }} {{ selectedCurrency.symbol.toUpperCase() }}
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-xs text-gray-500">Rate valid until:</span>
                <span class="text-xs text-gray-500">{{ formatTime(paymentEstimate.validUntil) }}</span>
              </div>
            </div>
            
            <div v-else-if="estimateLoading" class="flex items-center justify-center py-4">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span class="ml-2 text-sm text-gray-600">Getting current rates...</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Future Payment Methods Placeholder -->
      <div class="border border-gray-200 rounded-lg p-4 opacity-50">
        <div class="flex items-center">
          <input
            id="traditional-payment"
            type="radio"
            value="traditional"
            disabled
            class="h-4 w-4 text-gray-400 border-gray-300"
          />
          <label for="traditional-payment" class="ml-3 flex items-center">
            <span class="text-base font-medium text-gray-500">Credit Card / Bank Transfer</span>
            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
              Coming Soon
            </span>
          </label>
        </div>
        <p class="ml-7 mt-2 text-sm text-gray-500">Traditional payment methods will be available soon.</p>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-6 flex justify-between">
      <button
        @click="$emit('back')"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        Back to Cart
      </button>
      
      <button
        @click="proceedToPayment"
        :disabled="!canProceed"
        :class="[
          'px-6 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
          canProceed
            ? 'text-white bg-blue-600 hover:bg-blue-700'
            : 'text-gray-400 bg-gray-200 cursor-not-allowed'
        ]"
      >
        Continue to Payment
      </button>
    </div>

    <!-- Security Features -->
    <div class="mt-6 pt-6 border-t border-gray-200">
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-600">
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          Secure Payment
        </div>
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          No Hidden Fees
        </div>
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          Fast Processing
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { usePaymentStore } from '../stores/payment'

// Props
const props = defineProps({
  totalAmount: {
    type: Number,
    required: true
  }
})

// Emits
const emit = defineEmits(['back', 'continue'])

// Store
const paymentStore = usePaymentStore()

// Local state
const estimateLoading = ref(false)

// Computed properties
const availableCurrencies = computed(() => paymentStore.availableCurrencies)
const selectedCurrency = computed(() => paymentStore.selectedCurrency)
const paymentEstimate = computed(() => paymentStore.paymentEstimate)
const loading = computed(() => paymentStore.loading)
const error = computed(() => paymentStore.error)
const paymentMethod = ref('crypto')

const canProceed = computed(() => {
  return paymentMethod.value === 'crypto' && selectedCurrency.value && paymentEstimate.value
})

// Methods
const selectCurrency = async (currency) => {
  paymentStore.selectCurrency(currency)
  await getPaymentEstimate()
}

const getPaymentEstimate = async () => {
  if (!selectedCurrency.value || !props.totalAmount) return
  
  estimateLoading.value = true
  try {
    await paymentStore.getPaymentEstimate(props.totalAmount)
  } catch (err) {
    console.error('Failed to get payment estimate:', err)
  } finally {
    estimateLoading.value = false
  }
}

const refreshEstimate = async () => {
  await getPaymentEstimate()
}

const proceedToPayment = () => {
  if (canProceed.value) {
    emit('continue', {
      paymentMethod: paymentMethod.value,
      currency: selectedCurrency.value,
      estimate: paymentEstimate.value
    })
  }
}

const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US').format(price)
}

const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Watch for currency changes to update estimate
watch(selectedCurrency, async (newCurrency) => {
  if (newCurrency && props.totalAmount) {
    await getPaymentEstimate()
  }
})

// Initialize
onMounted(async () => {
  // Get initial estimate if currency is already selected
  if (selectedCurrency.value && props.totalAmount) {
    await getPaymentEstimate()
  }
})
</script>