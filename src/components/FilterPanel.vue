<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
      <button 
        @click="clearAllFilters"
        class="text-sm text-blue-600 hover:text-blue-800 font-medium"
        v-if="hasActiveFilters"
      >
        Clear All
      </button>
    </div>

    <div class="space-y-6">
      <!-- Search -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
        <div class="relative">
          <input
            v-model="filters.search"
            type="text"
            placeholder="Search artworks, artists..."
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            @input="updateFilters"
          />
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <!-- Category Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
        <div class="space-y-2">
          <label v-for="category in availableCategories" :key="category" class="flex items-center">
            <input
              type="checkbox"
              :value="category"
              v-model="filters.categories"
              @change="updateFilters"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">{{ category }}</span>
          </label>
        </div>
      </div>

      <!-- Artist Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Artist</label>
        <div class="space-y-2 max-h-40 overflow-y-auto">
          <label v-for="artist in availableArtists" :key="artist" class="flex items-center">
            <input
              type="checkbox"
              :value="artist"
              v-model="filters.artists"
              @change="updateFilters"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">{{ artist }}</span>
          </label>
        </div>
      </div>

      <!-- Medium Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Medium</label>
        <div class="space-y-2">
          <label v-for="medium in availableMediums" :key="medium" class="flex items-center">
            <input
              type="checkbox"
              :value="medium"
              v-model="filters.mediums"
              @change="updateFilters"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">{{ medium }}</span>
          </label>
        </div>
      </div>

      <!-- Price Range Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
        <div class="space-y-3">
          <div class="flex items-center space-x-2">
            <input
              v-model.number="filters.priceRange.min"
              type="number"
              placeholder="Min"
              min="0"
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              @input="updateFilters"
            />
            <span class="text-gray-500">to</span>
            <input
              v-model.number="filters.priceRange.max"
              type="number"
              placeholder="Max"
              min="0"
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              @input="updateFilters"
            />
          </div>
          
          <!-- Quick price range buttons -->
          <div class="flex flex-wrap gap-2">
            <button
              v-for="range in quickPriceRanges"
              :key="range.label"
              @click="setQuickPriceRange(range)"
              class="px-3 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors duration-200"
              :class="{ 'bg-blue-100 text-blue-700': isQuickRangeActive(range) }"
            >
              {{ range.label }}
            </button>
          </div>
        </div>
      </div>

      <!-- Style Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Style</label>
        <div class="space-y-2">
          <label v-for="style in availableStyles" :key="style" class="flex items-center">
            <input
              type="checkbox"
              :value="style"
              v-model="filters.styles"
              @change="updateFilters"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">{{ style }}</span>
          </label>
        </div>
      </div>
    </div>

    <!-- Active Filters Display -->
    <div v-if="hasActiveFilters" class="mt-6 pt-4 border-t border-gray-200">
      <h4 class="text-sm font-medium text-gray-700 mb-2">Active Filters:</h4>
      <div class="flex flex-wrap gap-2">
        <span
          v-for="filter in activeFiltersList"
          :key="filter.key"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
        >
          {{ filter.label }}
          <button
            @click="removeFilter(filter.type, filter.value)"
            class="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-200"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useArtworkStore } from '../stores/artwork'

const emit = defineEmits(['filtersChanged'])

const artworkStore = useArtworkStore()

const filters = ref({
  search: '',
  categories: [],
  artists: [],
  mediums: [],
  styles: [],
  priceRange: {
    min: null,
    max: null
  }
})

const quickPriceRanges = [
  { label: 'Under $5K', min: 0, max: 5000 },
  { label: '$5K - $10K', min: 5000, max: 10000 },
  { label: '$10K - $15K', min: 10000, max: 15000 },
  { label: 'Over $15K', min: 15000, max: null }
]

// Get unique values from artworks for filter options
const availableCategories = computed(() => {
  const categories = artworkStore.artworks.map(artwork => artwork.category)
  return [...new Set(categories)].sort()
})

const availableArtists = computed(() => {
  const artists = artworkStore.artworks.map(artwork => artwork.artist)
  return [...new Set(artists)].sort()
})

const availableMediums = computed(() => {
  const mediums = artworkStore.artworks.map(artwork => artwork.medium)
  return [...new Set(mediums)].sort()
})

const availableStyles = computed(() => {
  const styles = artworkStore.artworks.map(artwork => artwork.style)
  return [...new Set(styles)].sort()
})

const hasActiveFilters = computed(() => {
  return filters.value.search ||
         filters.value.categories.length > 0 ||
         filters.value.artists.length > 0 ||
         filters.value.mediums.length > 0 ||
         filters.value.styles.length > 0 ||
         filters.value.priceRange.min !== null ||
         filters.value.priceRange.max !== null
})

const activeFiltersList = computed(() => {
  const list = []
  
  if (filters.value.search) {
    list.push({ key: 'search', type: 'search', label: `Search: "${filters.value.search}"`, value: filters.value.search })
  }
  
  filters.value.categories.forEach(category => {
    list.push({ key: `category-${category}`, type: 'categories', label: `Category: ${category}`, value: category })
  })
  
  filters.value.artists.forEach(artist => {
    list.push({ key: `artist-${artist}`, type: 'artists', label: `Artist: ${artist}`, value: artist })
  })
  
  filters.value.mediums.forEach(medium => {
    list.push({ key: `medium-${medium}`, type: 'mediums', label: `Medium: ${medium}`, value: medium })
  })
  
  filters.value.styles.forEach(style => {
    list.push({ key: `style-${style}`, type: 'styles', label: `Style: ${style}`, value: style })
  })
  
  if (filters.value.priceRange.min !== null || filters.value.priceRange.max !== null) {
    const min = filters.value.priceRange.min || 0
    const max = filters.value.priceRange.max || '∞'
    list.push({ key: 'price', type: 'priceRange', label: `Price: $${min} - $${max}`, value: 'range' })
  }
  
  return list
})

const updateFilters = () => {
  emit('filtersChanged', { ...filters.value })
}

const clearAllFilters = () => {
  filters.value = {
    search: '',
    categories: [],
    artists: [],
    mediums: [],
    styles: [],
    priceRange: {
      min: null,
      max: null
    }
  }
  updateFilters()
}

const removeFilter = (type, value) => {
  if (type === 'search') {
    filters.value.search = ''
  } else if (type === 'priceRange') {
    filters.value.priceRange = { min: null, max: null }
  } else if (Array.isArray(filters.value[type])) {
    const index = filters.value[type].indexOf(value)
    if (index > -1) {
      filters.value[type].splice(index, 1)
    }
  }
  updateFilters()
}

const setQuickPriceRange = (range) => {
  filters.value.priceRange = {
    min: range.min,
    max: range.max
  }
  updateFilters()
}

const isQuickRangeActive = (range) => {
  return filters.value.priceRange.min === range.min && filters.value.priceRange.max === range.max
}

// Watch for changes in artworks to update filter options
watch(() => artworkStore.artworks, () => {
  // Filter options will be automatically updated due to computed properties
}, { deep: true })
</script>