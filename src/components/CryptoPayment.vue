<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <!-- Payment Header -->
    <div class="text-center mb-6">
      <div class="flex items-center justify-center mb-4">
        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
          <span class="text-xl">{{ paymentData.currency?.icon || '₿' }}</span>
        </div>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">
        Complete Your {{ paymentData.currency?.name || 'Cryptocurrency' }} Payment
      </h3>
      <p class="text-sm text-gray-600">
        Send the exact amount to the address below to complete your purchase
      </p>
    </div>

    <!-- Payment Status -->
    <div class="mb-6">
      <div class="flex items-center justify-center mb-4">
        <div :class="statusClasses" class="px-4 py-2 rounded-full text-sm font-medium">
          <div class="flex items-center">
            <div v-if="isLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
            <svg v-else-if="paymentStatus === 'completed'" class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <svg v-else-if="paymentStatus === 'failed' || paymentStatus === 'expired'" class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <div v-else class="w-2 h-2 bg-current rounded-full mr-2 animate-pulse"></div>
            {{ statusText }}
          </div>
        </div>
      </div>

      <!-- Timer -->
      <div v-if="timeRemaining > 0 && paymentStatus === 'pending'" class="text-center">
        <p class="text-sm text-gray-600 mb-2">Payment expires in:</p>
        <div class="text-2xl font-mono font-bold text-gray-900">
          {{ formatTime(timeRemaining) }}
        </div>
      </div>
    </div>

    <!-- Payment Details -->
    <div v-if="currentPayment" class="space-y-6">
      <!-- Amount Section -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-700 mb-3">Payment Amount</h4>
        <div class="space-y-2">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">USD Amount:</span>
            <span class="text-sm font-medium text-gray-900">${{ formatPrice(currentPayment.priceAmount) }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">{{ currentPayment.payCurrency?.toUpperCase() }} Amount:</span>
            <span class="text-lg font-bold text-gray-900">
              {{ formattedPaymentAmount }} {{ currentPayment.payCurrency?.toUpperCase() }}
            </span>
          </div>
        </div>
      </div>

      <!-- QR Code Section -->
      <div class="text-center">
        <h4 class="text-sm font-medium text-gray-700 mb-3">Scan QR Code</h4>
        <div class="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
          <QRCodeVue3
            :value="qrCodeData"
            :width="200"
            :height="200"
            :dots-options="{ type: 'rounded', color: '#1f2937' }"
            :corners-square-options="{ type: 'extra-rounded', color: '#1f2937' }"
            :corners-dot-options="{ type: 'dot', color: '#1f2937' }"
            :background-options="{ color: '#ffffff' }"
          />
        </div>
        <p class="text-xs text-gray-500 mt-2">
          Scan with your {{ currentPayment.payCurrency?.toUpperCase() }} wallet
        </p>
      </div>

      <!-- Address Section -->
      <div>
        <h4 class="text-sm font-medium text-gray-700 mb-3">Payment Address</h4>
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <code class="text-sm font-mono text-gray-900 break-all flex-1 mr-3">
              {{ currentPayment.payAddress }}
            </code>
            <button
              @click="copyAddress"
              :class="[
                'flex-shrink-0 px-3 py-1 text-xs font-medium rounded transition-colors duration-200',
                addressCopied
                  ? 'bg-green-100 text-green-800'
                  : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
              ]"
            >
              {{ addressCopied ? 'Copied!' : 'Copy' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Instructions -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-blue-900 mb-2">Payment Instructions</h4>
        <ul class="text-sm text-blue-800 space-y-1">
          <li>• Send exactly <strong>{{ formattedPaymentAmount }} {{ currentPayment.payCurrency?.toUpperCase() }}</strong> to the address above</li>
          <li>• Do not send any other cryptocurrency to this address</li>
          <li>• Payment will be confirmed automatically once received</li>
          <li>• Keep this page open to monitor payment status</li>
        </ul>
      </div>

      <!-- Transaction Details -->
      <div v-if="currentPayment.actuallyPaid" class="bg-green-50 border border-green-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-green-900 mb-2">Transaction Received</h4>
        <div class="text-sm text-green-800 space-y-1">
          <div>Amount Received: {{ currentPayment.actuallyPaid }} {{ currentPayment.payCurrency?.toUpperCase() }}</div>
          <div>Status: {{ statusText }}</div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Payment Error</h3>
          <p class="mt-1 text-sm text-red-700">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-6 flex flex-col sm:flex-row gap-3">
      <button
        v-if="paymentStatus === 'pending'"
        @click="checkPaymentStatus"
        :disabled="isLoading"
        class="flex-1 px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded-md hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {{ isLoading ? 'Checking...' : 'Check Payment Status' }}
      </button>
      
      <button
        v-if="paymentStatus === 'failed' || paymentStatus === 'expired'"
        @click="retryPayment"
        class="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
      >
        Try Again
      </button>
      
      <button
        v-if="paymentStatus === 'completed'"
        @click="viewOrder"
        class="flex-1 px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700"
      >
        View Order Details
      </button>
      
      <button
        @click="cancelPayment"
        :disabled="paymentStatus === 'completed'"
        class="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {{ paymentStatus === 'completed' ? 'Payment Complete' : 'Cancel Payment' }}
      </button>
    </div>

    <!-- Support Information -->
    <div class="mt-6 pt-6 border-t border-gray-200 text-center">
      <p class="text-sm text-gray-600 mb-2">Need help with your payment?</p>
      <div class="space-y-1 text-sm">
        <div>Order ID: <code class="font-mono text-gray-900">{{ currentPayment?.orderId }}</code></div>
        <div>Payment ID: <code class="font-mono text-gray-900">{{ currentPayment?.paymentId }}</code></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { usePaymentStore } from '../stores/payment'
import QRCodeVue3 from 'qrcode-vue3'
import paymentService from '../services/paymentService'

// Props
const props = defineProps({
  paymentData: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['payment-completed', 'payment-failed', 'payment-cancelled'])

// Router
const router = useRouter()

// Store
const paymentStore = usePaymentStore()

// Local state
const addressCopied = ref(false)
const isLoading = ref(false)
let statusCheckInterval = null

// Computed properties
const currentPayment = computed(() => paymentStore.currentPayment)
const paymentStatus = computed(() => paymentStore.paymentStatus)
const error = computed(() => paymentStore.error)
const timeRemaining = computed(() => paymentStore.paymentTimeRemaining)
const formattedPaymentAmount = computed(() => paymentStore.formattedPaymentAmount)

const qrCodeData = computed(() => {
  if (!currentPayment.value) return ''
  return paymentService.generateQRCodeData(
    currentPayment.value.payAddress,
    currentPayment.value.payAmount,
    currentPayment.value.payCurrency
  )
})

const statusText = computed(() => {
  const statusMap = {
    'pending': 'Waiting for Payment',
    'confirming': 'Confirming Transaction',
    'completed': 'Payment Completed',
    'failed': 'Payment Failed',
    'expired': 'Payment Expired'
  }
  return statusMap[paymentStatus.value] || 'Processing...'
})

const statusClasses = computed(() => {
  const classMap = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'confirming': 'bg-blue-100 text-blue-800',
    'completed': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800',
    'expired': 'bg-gray-100 text-gray-800'
  }
  return classMap[paymentStatus.value] || 'bg-gray-100 text-gray-800'
})

// Methods
const copyAddress = async () => {
  if (!currentPayment.value?.payAddress) return
  
  try {
    await navigator.clipboard.writeText(currentPayment.value.payAddress)
    addressCopied.value = true
    setTimeout(() => {
      addressCopied.value = false
    }, 2000)
  } catch (err) {
    console.error('Failed to copy address:', err)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = currentPayment.value.payAddress
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    addressCopied.value = true
    setTimeout(() => {
      addressCopied.value = false
    }, 2000)
  }
}

const checkPaymentStatus = async () => {
  if (!currentPayment.value?.paymentId) return
  
  isLoading.value = true
  try {
    await paymentStore.checkPaymentStatus()
  } catch (err) {
    console.error('Failed to check payment status:', err)
  } finally {
    isLoading.value = false
  }
}

const retryPayment = () => {
  paymentStore.resetPaymentFlow()
  emit('payment-failed')
}

const cancelPayment = () => {
  paymentStore.cancelPayment()
  emit('payment-cancelled')
}

const viewOrder = () => {
  if (currentPayment.value?.orderId) {
    router.push(`/order-confirmation/${currentPayment.value.orderId}`)
  }
}

const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US').format(price)
}

const formatTime = (milliseconds) => {
  const totalSeconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

const startStatusChecking = () => {
  if (statusCheckInterval) return
  
  statusCheckInterval = setInterval(async () => {
    if (paymentStatus.value === 'pending' || paymentStatus.value === 'confirming') {
      await checkPaymentStatus()
    }
  }, 15000) // Check every 15 seconds
}

const stopStatusChecking = () => {
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval)
    statusCheckInterval = null
  }
}

// Watch for payment status changes
watch(paymentStatus, (newStatus, oldStatus) => {
  if (newStatus === 'completed' && oldStatus !== 'completed') {
    stopStatusChecking()
    emit('payment-completed', currentPayment.value)
  } else if (newStatus === 'failed' || newStatus === 'expired') {
    stopStatusChecking()
    emit('payment-failed', currentPayment.value)
  }
})

// Watch for payment expiration
watch(timeRemaining, (remaining) => {
  if (remaining <= 0 && paymentStatus.value === 'pending') {
    stopStatusChecking()
    paymentStore.paymentStatus = 'expired'
  }
})

// Lifecycle
onMounted(() => {
  // Start status checking if payment is active
  if (paymentStatus.value === 'pending' || paymentStatus.value === 'confirming') {
    startStatusChecking()
  }
})

onUnmounted(() => {
  stopStatusChecking()
})
</script>