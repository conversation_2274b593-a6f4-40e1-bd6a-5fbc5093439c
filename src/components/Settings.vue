<template>
  <div class="settings">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h2 class="text-2xl font-bold text-gray-900">System Settings</h2>
        <p class="text-gray-600 mt-1">Configure system preferences and manage site settings</p>
      </div>
      <div class="flex items-center space-x-3">
        <!-- Save All Changes Button -->
        <button
          @click="saveAllSettings"
          :disabled="!hasUnsavedChanges"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
          </svg>
          Save All Changes
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Settings Navigation -->
      <div class="lg:col-span-1">
        <nav class="bg-white rounded-lg shadow border border-gray-200 p-4">
          <ul class="space-y-2">
            <li>
              <button
                @click="activeTab = 'general'"
                :class="[
                  'w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  activeTab === 'general' 
                    ? 'bg-primary-100 text-primary-700' 
                    : 'text-gray-700 hover:bg-gray-100'
                ]"
              >
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                  </svg>
                  General Settings
                </div>
              </button>
            </li>
            
            <li>
              <button
                @click="activeTab = 'categories'"
                :class="[
                  'w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  activeTab === 'categories' 
                    ? 'bg-primary-100 text-primary-700' 
                    : 'text-gray-700 hover:bg-gray-100'
                ]"
              >
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                  </svg>
                  Categories
                </div>
              </button>
            </li>
            
            <li>
              <button
                @click="activeTab = 'artists'"
                :class="[
                  'w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  activeTab === 'artists' 
                    ? 'bg-primary-100 text-primary-700' 
                    : 'text-gray-700 hover:bg-gray-100'
                ]"
              >
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                  </svg>
                  Artists
                </div>
              </button>
            </li>
            
            <li>
              <button
                @click="activeTab = 'payments'"
                :class="[
                  'w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  activeTab === 'payments' 
                    ? 'bg-primary-100 text-primary-700' 
                    : 'text-gray-700 hover:bg-gray-100'
                ]"
              >
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                  </svg>
                  Payment Settings
                </div>
              </button>
            </li>
            
            <li>
              <button
                @click="activeTab = 'notifications'"
                :class="[
                  'w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  activeTab === 'notifications' 
                    ? 'bg-primary-100 text-primary-700' 
                    : 'text-gray-700 hover:bg-gray-100'
                ]"
              >
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5v3"/>
                  </svg>
                  Notifications
                </div>
              </button>
            </li>
            
            <li>
              <button
                @click="activeTab = 'security'"
                :class="[
                  'w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors',
                  activeTab === 'security' 
                    ? 'bg-primary-100 text-primary-700' 
                    : 'text-gray-700 hover:bg-gray-100'
                ]"
              >
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                  </svg>
                  Security
                </div>
              </button>
            </li>
          </ul>
        </nav>
      </div>

      <!-- Settings Content -->
      <div class="lg:col-span-2">
        <!-- General Settings -->
        <div v-if="activeTab === 'general'" class="bg-white rounded-lg shadow border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">General Settings</h3>
          
          <div class="space-y-6">
            <!-- Site Name -->
            <div>
              <label for="siteName" class="block text-sm font-medium text-gray-700 mb-2">
                Site Name
              </label>
              <input
                id="siteName"
                v-model="settings.siteName"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <!-- Currency -->
            <div>
              <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">
                Default Currency
              </label>
              <select
                id="currency"
                v-model="settings.currency"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="USD">USD - US Dollar</option>
                <option value="EUR">EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
                <option value="CAD">CAD - Canadian Dollar</option>
              </select>
            </div>

            <!-- Tax Rate -->
            <div>
              <label for="taxRate" class="block text-sm font-medium text-gray-700 mb-2">
                Tax Rate (%)
              </label>
              <input
                id="taxRate"
                v-model.number="settings.taxRate"
                type="number"
                min="0"
                max="100"
                step="0.01"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <!-- Shipping Rate -->
            <div>
              <label for="shippingRate" class="block text-sm font-medium text-gray-700 mb-2">
                Default Shipping Rate ($)
              </label>
              <input
                id="shippingRate"
                v-model.number="settings.shippingRate"
                type="number"
                min="0"
                step="0.01"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <!-- Maintenance Mode -->
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">Maintenance Mode</label>
                <p class="text-sm text-gray-500">Enable to temporarily disable public access</p>
              </div>
              <button
                @click="settings.maintenanceMode = !settings.maintenanceMode"
                :class="[
                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                  settings.maintenanceMode ? 'bg-primary-600' : 'bg-gray-200'
                ]"
              >
                <span
                  :class="[
                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                    settings.maintenanceMode ? 'translate-x-5' : 'translate-x-0'
                  ]"
                ></span>
              </button>
            </div>

            <!-- Allow Registration -->
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">Allow User Registration</label>
                <p class="text-sm text-gray-500">Allow new users to create accounts</p>
              </div>
              <button
                @click="settings.allowRegistration = !settings.allowRegistration"
                :class="[
                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                  settings.allowRegistration ? 'bg-primary-600' : 'bg-gray-200'
                ]"
              >
                <span
                  :class="[
                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                    settings.allowRegistration ? 'translate-x-5' : 'translate-x-0'
                  ]"
                ></span>
              </button>
            </div>
          </div>
        </div>

        <!-- Categories Management -->
        <div v-if="activeTab === 'categories'" class="bg-white rounded-lg shadow border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Category Management</h3>
            <button
              @click="showAddCategoryForm = true"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
              </svg>
              Add Category
            </button>
          </div>

          <!-- Add Category Form -->
          <div v-if="showAddCategoryForm" class="mb-6 p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-3">
              <input
                v-model="newCategoryName"
                type="text"
                placeholder="Category name"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                @keyup.enter="addCategory"
              />
              <button
                @click="addCategory"
                :disabled="!newCategoryName.trim()"
                class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add
              </button>
              <button
                @click="cancelAddCategory"
                class="px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
            </div>
          </div>

          <!-- Categories List -->
          <div class="space-y-2">
            <div
              v-for="category in categories"
              :key="category"
              class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <span class="text-sm font-medium text-gray-900">{{ category }}</span>
              <button
                @click="removeCategory(category)"
                class="text-red-600 hover:text-red-800 text-sm"
              >
                Remove
              </button>
            </div>
          </div>
        </div>

        <!-- Artists Management -->
        <div v-if="activeTab === 'artists'" class="bg-white rounded-lg shadow border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Artist Management</h3>
            <button
              @click="showAddArtistForm = true"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
              </svg>
              Add Artist
            </button>
          </div>

          <!-- Add Artist Form -->
          <div v-if="showAddArtistForm" class="mb-6 p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-3">
              <input
                v-model="newArtistName"
                type="text"
                placeholder="Artist name"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                @keyup.enter="addArtist"
              />
              <button
                @click="addArtist"
                :disabled="!newArtistName.trim()"
                class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add
              </button>
              <button
                @click="cancelAddArtist"
                class="px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
            </div>
          </div>

          <!-- Artists List -->
          <div class="space-y-2">
            <div
              v-for="artist in artists"
              :key="artist"
              class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <span class="text-sm font-medium text-gray-900">{{ artist }}</span>
              <button
                @click="removeArtist(artist)"
                class="text-red-600 hover:text-red-800 text-sm"
              >
                Remove
              </button>
            </div>
          </div>
        </div>

        <!-- Payment Settings -->
        <div v-if="activeTab === 'payments'" class="bg-white rounded-lg shadow border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Settings</h3>
          
          <div class="space-y-6">
            <!-- Cryptocurrency Payments -->
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">Accept Cryptocurrency</label>
                <p class="text-sm text-gray-500">Enable cryptocurrency payments via NOWPayments</p>
              </div>
              <button
                @click="paymentSettings.acceptCrypto = !paymentSettings.acceptCrypto"
                :class="[
                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                  paymentSettings.acceptCrypto ? 'bg-primary-600' : 'bg-gray-200'
                ]"
              >
                <span
                  :class="[
                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                    paymentSettings.acceptCrypto ? 'translate-x-5' : 'translate-x-0'
                  ]"
                ></span>
              </button>
            </div>

            <!-- NOWPayments API Key -->
            <div v-if="paymentSettings.acceptCrypto">
              <label for="nowpaymentsApiKey" class="block text-sm font-medium text-gray-700 mb-2">
                NOWPayments API Key
              </label>
              <input
                id="nowpaymentsApiKey"
                v-model="paymentSettings.nowpaymentsApiKey"
                type="password"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Enter your NOWPayments API key"
              />
            </div>

            <!-- Minimum Order Amount -->
            <div>
              <label for="minOrderAmount" class="block text-sm font-medium text-gray-700 mb-2">
                Minimum Order Amount ($)
              </label>
              <input
                id="minOrderAmount"
                v-model.number="paymentSettings.minOrderAmount"
                type="number"
                min="0"
                step="0.01"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
        </div>

        <!-- Notification Settings -->
        <div v-if="activeTab === 'notifications'" class="bg-white rounded-lg shadow border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Notification Settings</h3>
          
          <div class="space-y-6">
            <!-- Email Notifications -->
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">Email Notifications</label>
                <p class="text-sm text-gray-500">Send email notifications for orders and updates</p>
              </div>
              <button
                @click="notificationSettings.emailNotifications = !notificationSettings.emailNotifications"
                :class="[
                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                  notificationSettings.emailNotifications ? 'bg-primary-600' : 'bg-gray-200'
                ]"
              >
                <span
                  :class="[
                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                    notificationSettings.emailNotifications ? 'translate-x-5' : 'translate-x-0'
                  ]"
                ></span>
              </button>
            </div>

            <!-- Order Notifications -->
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">Order Notifications</label>
                <p class="text-sm text-gray-500">Notify admins of new orders</p>
              </div>
              <button
                @click="notificationSettings.orderNotifications = !notificationSettings.orderNotifications"
                :class="[
                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                  notificationSettings.orderNotifications ? 'bg-primary-600' : 'bg-gray-200'
                ]"
              >
                <span
                  :class="[
                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                    notificationSettings.orderNotifications ? 'translate-x-5' : 'translate-x-0'
                  ]"
                ></span>
              </button>
            </div>

            <!-- Admin Email -->
            <div>
              <label for="adminEmail" class="block text-sm font-medium text-gray-700 mb-2">
                Admin Email Address
              </label>
              <input
                id="adminEmail"
                v-model="notificationSettings.adminEmail"
                type="email"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="<EMAIL>"
              />
            </div>
          </div>
        </div>

        <!-- Security Settings -->
        <div v-if="activeTab === 'security'" class="bg-white rounded-lg shadow border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Security Settings</h3>
          
          <div class="space-y-6">
            <!-- Session Timeout -->
            <div>
              <label for="sessionTimeout" class="block text-sm font-medium text-gray-700 mb-2">
                Session Timeout (minutes)
              </label>
              <select
                id="sessionTimeout"
                v-model="securitySettings.sessionTimeout"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="30">30 minutes</option>
                <option value="60">1 hour</option>
                <option value="120">2 hours</option>
                <option value="240">4 hours</option>
                <option value="480">8 hours</option>
              </select>
            </div>

            <!-- Password Requirements -->
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">Strong Password Requirements</label>
                <p class="text-sm text-gray-500">Require uppercase, lowercase, numbers, and symbols</p>
              </div>
              <button
                @click="securitySettings.strongPasswords = !securitySettings.strongPasswords"
                :class="[
                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                  securitySettings.strongPasswords ? 'bg-primary-600' : 'bg-gray-200'
                ]"
              >
                <span
                  :class="[
                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                    securitySettings.strongPasswords ? 'translate-x-5' : 'translate-x-0'
                  ]"
                ></span>
              </button>
            </div>

            <!-- Two-Factor Authentication -->
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">Two-Factor Authentication</label>
                <p class="text-sm text-gray-500">Require 2FA for admin accounts</p>
              </div>
              <button
                @click="securitySettings.twoFactorAuth = !securitySettings.twoFactorAuth"
                :class="[
                  'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                  securitySettings.twoFactorAuth ? 'bg-primary-600' : 'bg-gray-200'
                ]"
              >
                <span
                  :class="[
                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                    securitySettings.twoFactorAuth ? 'translate-x-5' : 'translate-x-0'
                  ]"
                ></span>
              </button>
            </div>

            <!-- Login Attempts -->
            <div>
              <label for="maxLoginAttempts" class="block text-sm font-medium text-gray-700 mb-2">
                Max Login Attempts
              </label>
              <select
                id="maxLoginAttempts"
                v-model="securitySettings.maxLoginAttempts"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="3">3 attempts</option>
                <option value="5">5 attempts</option>
                <option value="10">10 attempts</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Message -->
    <div
      v-if="showSuccessMessage"
      class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50"
    >
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
        </svg>
        Settings saved successfully!
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAdminStore } from '../stores/admin'

// Store
const adminStore = useAdminStore()

// Reactive state
const activeTab = ref('general')
const showSuccessMessage = ref(false)
const showAddCategoryForm = ref(false)
const showAddArtistForm = ref(false)
const newCategoryName = ref('')
const newArtistName = ref('')

// Settings data
const settings = ref({
  siteName: 'Contemporary Art Store',
  currency: 'USD',
  taxRate: 8.0,
  shippingRate: 50.0,
  maintenanceMode: false,
  allowRegistration: true
})

const paymentSettings = ref({
  acceptCrypto: true,
  nowpaymentsApiKey: '',
  minOrderAmount: 100
})

const notificationSettings = ref({
  emailNotifications: true,
  orderNotifications: true,
  adminEmail: '<EMAIL>'
})

const securitySettings = ref({
  sessionTimeout: 60,
  strongPasswords: true,
  twoFactorAuth: false,
  maxLoginAttempts: 5
})

// Original settings for comparison
const originalSettings = ref({})

// Computed properties
const categories = computed(() => adminStore.categories)
const artists = computed(() => adminStore.artists)

const hasUnsavedChanges = computed(() => {
  return JSON.stringify(settings.value) !== JSON.stringify(originalSettings.value.general) ||
         JSON.stringify(paymentSettings.value) !== JSON.stringify(originalSettings.value.payments) ||
         JSON.stringify(notificationSettings.value) !== JSON.stringify(originalSettings.value.notifications) ||
         JSON.stringify(securitySettings.value) !== JSON.stringify(originalSettings.value.security)
})

// Methods
const saveAllSettings = async () => {
  try {
    // Save general settings
    await adminStore.updateSystemSettings(settings.value)
    
    // Update original settings
    originalSettings.value = {
      general: { ...settings.value },
      payments: { ...paymentSettings.value },
      notifications: { ...notificationSettings.value },
      security: { ...securitySettings.value }
    }
    
    // Show success message
    showSuccessMessage.value = true
    setTimeout(() => {
      showSuccessMessage.value = false
    }, 3000)
  } catch (error) {
    console.error('Error saving settings:', error)
  }
}

const addCategory = async () => {
  if (newCategoryName.value.trim()) {
    try {
      await adminStore.addCategory(newCategoryName.value.trim())
      newCategoryName.value = ''
      showAddCategoryForm.value = false
    } catch (error) {
      console.error('Error adding category:', error)
    }
  }
}

const cancelAddCategory = () => {
  newCategoryName.value = ''
  showAddCategoryForm.value = false
}

const removeCategory = async (categoryName) => {
  if (confirm(`Are you sure you want to remove the category "${categoryName}"?`)) {
    try {
      await adminStore.removeCategory(categoryName)
    } catch (error) {
      console.error('Error removing category:', error)
    }
  }
}

const addArtist = async () => {
  if (newArtistName.value.trim()) {
    try {
      await adminStore.addArtist(newArtistName.value.trim())
      newArtistName.value = ''
      showAddArtistForm.value = false
    } catch (error) {
      console.error('Error adding artist:', error)
    }
  }
}

const cancelAddArtist = () => {
  newArtistName.value = ''
  showAddArtistForm.value = false
}

const removeArtist = async (artistName) => {
  if (confirm(`Are you sure you want to remove the artist "${artistName}"?`)) {
    try {
      await adminStore.removeArtist(artistName)
    } catch (error) {
      console.error('Error removing artist:', error)
    }
  }
}

// Watchers
watch(() => adminStore.systemSettings, (newSettings) => {
  if (newSettings) {
    settings.value = { ...newSettings }
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  // Initialize original settings
  originalSettings.value = {
    general: { ...settings.value },
    payments: { ...paymentSettings.value },
    notifications: { ...notificationSettings.value },
    security: { ...securitySettings.value }
  }
})
</script>

<style scoped>
.settings {
  max-width: 100%;
}

/* Toggle switch animations */
.settings button[class*="relative inline-flex"] {
  transition: background-color 0.2s ease-in-out;
}

.settings button[class*="relative inline-flex"] span {
  transition: transform 0.2s ease-in-out;
}

/* Success message animation */
.settings .fixed.bottom-4 {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>