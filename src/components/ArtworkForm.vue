<template>
  <div class="artwork-form">
    <!-- Form Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h2 class="text-2xl font-bold text-gray-900">
          {{ isEditing ? 'Edit Artwork' : 'Add New Artwork' }}
        </h2>
        <p class="text-gray-600 mt-1">
          {{ isEditing ? 'Update artwork information' : 'Add a new artwork to the collection' }}
        </p>
      </div>
      <button
        @click="$emit('close')"
        class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
      </button>
    </div>

    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Basic Information -->
      <div class="bg-white p-6 rounded-lg border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Title -->
          <div>
            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              id="title"
              v-model="form.title"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter artwork title"
            />
            <p v-if="errors.title" class="mt-1 text-sm text-red-600">{{ errors.title }}</p>
          </div>

          <!-- Artist -->
          <div>
            <label for="artist" class="block text-sm font-medium text-gray-700 mb-2">
              Artist *
            </label>
            <div class="relative">
              <input
                id="artist"
                v-model="form.artist"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Enter artist name"
                @input="filterArtists"
                @focus="showArtistSuggestions = true"
              />
              <!-- Artist Suggestions -->
              <div
                v-if="showArtistSuggestions && filteredArtists.length > 0"
                class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto"
              >
                <button
                  v-for="artist in filteredArtists"
                  :key="artist"
                  type="button"
                  @click="selectArtist(artist)"
                  class="w-full px-3 py-2 text-left hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
                >
                  {{ artist }}
                </button>
              </div>
            </div>
            <p v-if="errors.artist" class="mt-1 text-sm text-red-600">{{ errors.artist }}</p>
          </div>

          <!-- Category -->
          <div>
            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
              Category *
            </label>
            <select
              id="category"
              v-model="form.category"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Select a category</option>
              <option v-for="category in categories" :key="category" :value="category">
                {{ category }}
              </option>
            </select>
            <p v-if="errors.category" class="mt-1 text-sm text-red-600">{{ errors.category }}</p>
          </div>

          <!-- Medium -->
          <div>
            <label for="medium" class="block text-sm font-medium text-gray-700 mb-2">
              Medium *
            </label>
            <input
              id="medium"
              v-model="form.medium"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g., Oil on Canvas, Bronze, Mixed Media"
            />
            <p v-if="errors.medium" class="mt-1 text-sm text-red-600">{{ errors.medium }}</p>
          </div>

          <!-- Style -->
          <div>
            <label for="style" class="block text-sm font-medium text-gray-700 mb-2">
              Style
            </label>
            <input
              id="style"
              v-model="form.style"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g., Abstract Expressionism, Pop Art"
            />
          </div>

          <!-- Price -->
          <div>
            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
              Price (USD) *
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                id="price"
                v-model.number="form.price"
                type="number"
                min="0"
                step="0.01"
                required
                class="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="0.00"
              />
            </div>
            <p v-if="errors.price" class="mt-1 text-sm text-red-600">{{ errors.price }}</p>
          </div>
        </div>

        <!-- Description -->
        <div class="mt-6">
          <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            v-model="form.description"
            rows="4"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            placeholder="Describe the artwork, its significance, and any relevant details..."
          ></textarea>
          <p v-if="errors.description" class="mt-1 text-sm text-red-600">{{ errors.description }}</p>
        </div>
      </div>

      <!-- Image Upload -->
      <div class="bg-white p-6 rounded-lg border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Artwork Image</h3>
        
        <!-- Current Image (if editing) -->
        <div v-if="isEditing && form.imageUrl" class="mb-4">
          <p class="text-sm text-gray-700 mb-2">Current Image:</p>
          <div class="relative inline-block">
            <img
              :src="form.imageUrl"
              :alt="form.title"
              class="w-32 h-32 object-cover rounded-lg border border-gray-300"
            />
          </div>
        </div>

        <!-- Image URL Input -->
        <div>
          <label for="imageUrl" class="block text-sm font-medium text-gray-700 mb-2">
            Image URL *
          </label>
          <input
            id="imageUrl"
            v-model="form.imageUrl"
            type="url"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            placeholder="https://example.com/artwork-image.jpg"
            @blur="validateImageUrl"
          />
          <p class="mt-1 text-sm text-gray-500">
            Enter a valid URL to an image of the artwork
          </p>
          <p v-if="errors.imageUrl" class="mt-1 text-sm text-red-600">{{ errors.imageUrl }}</p>
        </div>

        <!-- Image Preview -->
        <div v-if="form.imageUrl && !errors.imageUrl" class="mt-4">
          <p class="text-sm text-gray-700 mb-2">Preview:</p>
          <div class="relative inline-block">
            <img
              :src="form.imageUrl"
              :alt="form.title || 'Artwork preview'"
              class="w-48 h-48 object-cover rounded-lg border border-gray-300"
              @error="handleImageError"
              @load="handleImageLoad"
            />
            <div v-if="imageLoading" class="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
        <button
          type="button"
          @click="$emit('close')"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="loading || !isFormValid"
          class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="loading" class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            {{ isEditing ? 'Updating...' : 'Creating...' }}
          </span>
          <span v-else>
            {{ isEditing ? 'Update Artwork' : 'Create Artwork' }}
          </span>
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useAdminStore } from '../stores/admin'

// Props
const props = defineProps({
  artwork: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'submit'])

// Store
const adminStore = useAdminStore()

// Reactive state
const loading = ref(false)
const imageLoading = ref(false)
const showArtistSuggestions = ref(false)
const filteredArtists = ref([])

const form = ref({
  title: '',
  artist: '',
  category: '',
  medium: '',
  style: '',
  price: null,
  description: '',
  imageUrl: ''
})

const errors = ref({})

// Computed properties
const isEditing = computed(() => !!props.artwork)

const categories = computed(() => adminStore.categories)
const artists = computed(() => adminStore.artists)

const isFormValid = computed(() => {
  return form.value.title &&
         form.value.artist &&
         form.value.category &&
         form.value.medium &&
         form.value.price > 0 &&
         form.value.description &&
         form.value.imageUrl &&
         Object.keys(errors.value).length === 0
})

// Methods
const validateForm = () => {
  errors.value = {}

  if (!form.value.title.trim()) {
    errors.value.title = 'Title is required'
  }

  if (!form.value.artist.trim()) {
    errors.value.artist = 'Artist is required'
  }

  if (!form.value.category) {
    errors.value.category = 'Category is required'
  }

  if (!form.value.medium.trim()) {
    errors.value.medium = 'Medium is required'
  }

  if (!form.value.price || form.value.price <= 0) {
    errors.value.price = 'Price must be greater than 0'
  }

  if (!form.value.description.trim()) {
    errors.value.description = 'Description is required'
  }

  if (!form.value.imageUrl.trim()) {
    errors.value.imageUrl = 'Image URL is required'
  } else if (!isValidUrl(form.value.imageUrl)) {
    errors.value.imageUrl = 'Please enter a valid URL'
  }

  return Object.keys(errors.value).length === 0
}

const isValidUrl = (string) => {
  try {
    new URL(string)
    return true
  } catch (_) {
    return false
  }
}

const validateImageUrl = () => {
  if (form.value.imageUrl && !isValidUrl(form.value.imageUrl)) {
    errors.value.imageUrl = 'Please enter a valid URL'
  } else {
    delete errors.value.imageUrl
  }
}

const handleImageError = () => {
  imageLoading.value = false
  errors.value.imageUrl = 'Unable to load image from this URL'
}

const handleImageLoad = () => {
  imageLoading.value = false
  delete errors.value.imageUrl
}

const filterArtists = () => {
  const query = form.value.artist.toLowerCase()
  filteredArtists.value = artists.value.filter(artist =>
    artist.toLowerCase().includes(query) && artist.toLowerCase() !== query
  )
}

const selectArtist = (artist) => {
  form.value.artist = artist
  showArtistSuggestions.value = false
  filteredArtists.value = []
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    const artworkData = {
      ...form.value,
      id: isEditing.value ? props.artwork.id : undefined,
      createdAt: isEditing.value ? props.artwork.createdAt : new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    emit('submit', artworkData)
  } catch (error) {
    console.error('Form submission error:', error)
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  form.value = {
    title: '',
    artist: '',
    category: '',
    medium: '',
    style: '',
    price: null,
    description: '',
    imageUrl: ''
  }
  errors.value = {}
}

// Watchers
watch(() => form.value.imageUrl, (newUrl) => {
  if (newUrl) {
    imageLoading.value = true
  }
})

watch(() => props.artwork, (newArtwork) => {
  if (newArtwork) {
    form.value = {
      title: newArtwork.title || '',
      artist: newArtwork.artist || '',
      category: newArtwork.category || '',
      medium: newArtwork.medium || '',
      style: newArtwork.style || '',
      price: newArtwork.price || null,
      description: newArtwork.description || '',
      imageUrl: newArtwork.imageUrl || ''
    }
  } else {
    resetForm()
  }
}, { immediate: true })

// Click outside to close artist suggestions
const handleClickOutside = (event) => {
  if (!event.target.closest('.relative')) {
    showArtistSuggestions.value = false
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// Cleanup
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.artwork-form {
  max-width: 4xl;
}

/* Custom scrollbar for artist suggestions */
.max-h-40::-webkit-scrollbar {
  width: 4px;
}

.max-h-40::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.max-h-40::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.max-h-40::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>