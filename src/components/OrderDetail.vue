<template>
  <div class="order-detail">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center space-x-4">
        <button
          @click="$emit('close')"
          class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
          </svg>
        </button>
        <div>
          <h2 class="text-2xl font-bold text-gray-900">Order #{{ order.id }}</h2>
          <p class="text-gray-600 mt-1">Order details and management</p>
        </div>
      </div>
      
      <div class="flex items-center space-x-3">
        <!-- Print Button -->
        <button
          @click="printOrder"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"/>
          </svg>
          Print
        </button>
        
        <!-- Email Customer Button -->
        <button
          @click="emailCustomer"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
          Email Customer
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Main Order Information -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Order Status -->
        <div class="bg-white p-6 rounded-lg border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Status</h3>
          
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div :class="[
                'w-3 h-3 rounded-full',
                getStatusColor(order.status)
              ]"></div>
              <span class="text-lg font-medium text-gray-900 capitalize">{{ order.status }}</span>
            </div>
            
            <select
              :value="order.status"
              @change="updateStatus($event.target.value)"
              class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <!-- Status Timeline -->
          <div class="space-y-3">
            <div class="flex items-center space-x-3">
              <div :class="[
                'w-2 h-2 rounded-full',
                isStatusReached('pending') ? 'bg-green-500' : 'bg-gray-300'
              ]"></div>
              <span class="text-sm text-gray-600">Order Placed</span>
              <span class="text-sm text-gray-500">{{ formatDateTime(order.createdAt) }}</span>
            </div>
            
            <div class="flex items-center space-x-3">
              <div :class="[
                'w-2 h-2 rounded-full',
                isStatusReached('processing') ? 'bg-green-500' : 'bg-gray-300'
              ]"></div>
              <span class="text-sm text-gray-600">Processing</span>
              <span v-if="isStatusReached('processing')" class="text-sm text-gray-500">{{ formatDateTime(order.updatedAt) }}</span>
            </div>
            
            <div class="flex items-center space-x-3">
              <div :class="[
                'w-2 h-2 rounded-full',
                isStatusReached('shipped') ? 'bg-green-500' : 'bg-gray-300'
              ]"></div>
              <span class="text-sm text-gray-600">Shipped</span>
              <span v-if="order.trackingNumber" class="text-sm text-primary-600">{{ order.trackingNumber }}</span>
            </div>
            
            <div class="flex items-center space-x-3">
              <div :class="[
                'w-2 h-2 rounded-full',
                isStatusReached('delivered') ? 'bg-green-500' : 'bg-gray-300'
              ]"></div>
              <span class="text-sm text-gray-600">Delivered</span>
            </div>
          </div>
        </div>

        <!-- Artwork Information -->
        <div class="bg-white p-6 rounded-lg border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Artwork Details</h3>
          
          <div class="flex items-start space-x-4">
            <img
              :src="artwork?.imageUrl"
              :alt="artwork?.title"
              class="w-24 h-24 object-cover rounded-lg border border-gray-300"
              @error="handleImageError"
            />
            
            <div class="flex-1">
              <h4 class="text-lg font-medium text-gray-900">{{ artwork?.title || 'Unknown Artwork' }}</h4>
              <p class="text-gray-600 mt-1">by {{ artwork?.artist || 'Unknown Artist' }}</p>
              <p class="text-sm text-gray-500 mt-2">{{ artwork?.medium }}</p>
              <p class="text-sm text-gray-500">{{ artwork?.category }}</p>
              
              <div class="mt-3">
                <span class="text-lg font-semibold text-gray-900">${{ order.totalAmount.toLocaleString() }}</span>
              </div>
            </div>
          </div>
          
          <div v-if="artwork?.description" class="mt-4 pt-4 border-t border-gray-200">
            <p class="text-sm text-gray-600">{{ artwork.description }}</p>
          </div>
        </div>

        <!-- Payment Information -->
        <div class="bg-white p-6 rounded-lg border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Information</h3>
          
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">Payment Method</label>
              <p class="mt-1 text-sm text-gray-900">{{ formatPaymentMethod(order.paymentMethod) }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700">Payment Status</label>
              <p class="mt-1">
                <span :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  order.status === 'cancelled' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                ]">
                  {{ order.status === 'cancelled' ? 'Refunded' : 'Paid' }}
                </span>
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700">Subtotal</label>
              <p class="mt-1 text-sm text-gray-900">${{ order.totalAmount.toLocaleString() }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700">Total</label>
              <p class="mt-1 text-lg font-semibold text-gray-900">${{ order.totalAmount.toLocaleString() }}</p>
            </div>
          </div>
        </div>

        <!-- Order Notes -->
        <div class="bg-white p-6 rounded-lg border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Notes</h3>
          
          <div class="space-y-3">
            <div v-for="note in orderNotes" :key="note.id" class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div class="flex-1">
                <p class="text-sm text-gray-900">{{ note.content }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ note.author }} • {{ formatDateTime(note.createdAt) }}</p>
              </div>
            </div>
          </div>
          
          <!-- Add Note -->
          <div class="mt-4 pt-4 border-t border-gray-200">
            <div class="flex space-x-3">
              <input
                v-model="newNote"
                type="text"
                placeholder="Add a note..."
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                @keyup.enter="addNote"
              />
              <button
                @click="addNote"
                :disabled="!newNote.trim()"
                class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add Note
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Customer Information -->
        <div class="bg-white p-6 rounded-lg border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
          
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700">Name</label>
              <p class="mt-1 text-sm text-gray-900">{{ order.customerName }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700">Email</label>
              <p class="mt-1 text-sm text-gray-900">
                <a :href="`mailto:${order.customerEmail}`" class="text-primary-600 hover:text-primary-500">
                  {{ order.customerEmail }}
                </a>
              </p>
            </div>
          </div>
          
          <div class="mt-4 pt-4 border-t border-gray-200">
            <button
              @click="viewCustomerProfile"
              class="w-full px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              View Customer Profile
            </button>
          </div>
        </div>

        <!-- Shipping Information -->
        <div class="bg-white p-6 rounded-lg border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Shipping Information</h3>
          
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700">Address</label>
              <div class="mt-1 text-sm text-gray-900">
                <p>{{ order.shippingAddress?.street }}</p>
                <p>{{ order.shippingAddress?.city }}, {{ order.shippingAddress?.state }} {{ order.shippingAddress?.zipCode }}</p>
                <p>{{ order.shippingAddress?.country }}</p>
              </div>
            </div>
            
            <div v-if="order.trackingNumber">
              <label class="block text-sm font-medium text-gray-700">Tracking Number</label>
              <p class="mt-1 text-sm text-primary-600 font-medium">{{ order.trackingNumber }}</p>
            </div>
          </div>
          
          <div v-if="order.status === 'processing'" class="mt-4 pt-4 border-t border-gray-200">
            <button
              @click="generateTrackingNumber"
              class="w-full px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Generate Tracking Number
            </button>
          </div>
        </div>

        <!-- Order Timeline -->
        <div class="bg-white p-6 rounded-lg border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Timeline</h3>
          
          <div class="space-y-3">
            <div class="flex items-center space-x-3">
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">Order Placed</p>
                <p class="text-xs text-gray-500">{{ formatDateTime(order.createdAt) }}</p>
              </div>
            </div>
            
            <div v-if="order.updatedAt !== order.createdAt" class="flex items-center space-x-3">
              <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">Status Updated</p>
                <p class="text-xs text-gray-500">{{ formatDateTime(order.updatedAt) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white p-6 rounded-lg border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          
          <div class="space-y-2">
            <button
              v-if="order.status !== 'cancelled'"
              @click="cancelOrder"
              class="w-full px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Cancel Order
            </button>
            
            <button
              @click="duplicateOrder"
              class="w-full px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Duplicate Order
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useArtworkStore } from '../stores/artwork'
import { useAuthStore } from '../stores/auth'

// Props
const props = defineProps({
  order: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['close', 'update-status', 'cancel-order'])

// Stores
const artworkStore = useArtworkStore()
const authStore = useAuthStore()

// Reactive state
const newNote = ref('')
const orderNotes = ref([
  {
    id: 1,
    content: 'Order received and payment confirmed',
    author: 'System',
    createdAt: props.order.createdAt
  }
])

// Computed properties
const artwork = computed(() => artworkStore.getArtworkById(props.order.artworkId))

// Methods
const updateStatus = async (newStatus) => {
  try {
    emit('update-status', { orderId: props.order.id, status: newStatus })
    
    // Add note for status change
    orderNotes.value.push({
      id: Date.now(),
      content: `Order status changed to ${newStatus}`,
      author: authStore.user?.name || 'Admin',
      createdAt: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error updating order status:', error)
  }
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'bg-yellow-500',
    processing: 'bg-blue-500',
    shipped: 'bg-purple-500',
    delivered: 'bg-green-500',
    completed: 'bg-green-500',
    cancelled: 'bg-red-500'
  }
  return colors[status] || 'bg-gray-500'
}

const isStatusReached = (targetStatus) => {
  const statusOrder = ['pending', 'processing', 'shipped', 'delivered', 'completed']
  const currentIndex = statusOrder.indexOf(props.order.status)
  const targetIndex = statusOrder.indexOf(targetStatus)
  
  return currentIndex >= targetIndex || props.order.status === 'completed'
}

const formatPaymentMethod = (method) => {
  const methods = {
    cryptocurrency: 'Cryptocurrency',
    credit_card: 'Credit Card',
    bank_transfer: 'Bank Transfer'
  }
  return methods[method] || method
}

const formatDateTime = (dateString) => {
  return new Date(dateString).toLocaleString()
}

const addNote = () => {
  if (newNote.value.trim()) {
    orderNotes.value.push({
      id: Date.now(),
      content: newNote.value.trim(),
      author: authStore.user?.name || 'Admin',
      createdAt: new Date().toISOString()
    })
    newNote.value = ''
  }
}

const handleImageError = (event) => {
  event.target.src = '/placeholder-image.jpg'
}

const printOrder = () => {
  window.print()
}

const emailCustomer = () => {
  const subject = `Order Update - #${props.order.id}`
  const body = `Dear ${props.order.customerName},\n\nYour order #${props.order.id} status: ${props.order.status}\n\nBest regards,\nContemporary Art Store`
  window.location.href = `mailto:${props.order.customerEmail}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
}

const viewCustomerProfile = () => {
  // Navigate to customer profile or show customer details
  console.log('View customer profile:', props.order.customerEmail)
}

const generateTrackingNumber = () => {
  const trackingNumber = `TRK${props.order.id}${Date.now().toString().slice(-6)}`
  
  // Update order with tracking number
  emit('update-status', { 
    orderId: props.order.id, 
    status: 'shipped',
    trackingNumber 
  })
  
  orderNotes.value.push({
    id: Date.now(),
    content: `Tracking number generated: ${trackingNumber}`,
    author: authStore.user?.name || 'Admin',
    createdAt: new Date().toISOString()
  })
}

const cancelOrder = () => {
  if (confirm('Are you sure you want to cancel this order?')) {
    emit('cancel-order', props.order.id)
    
    orderNotes.value.push({
      id: Date.now(),
      content: 'Order cancelled by admin',
      author: authStore.user?.name || 'Admin',
      createdAt: new Date().toISOString()
    })
  }
}

const duplicateOrder = () => {
  // Logic to create a new order with same details
  console.log('Duplicate order:', props.order.id)
}

// Lifecycle
onMounted(() => {
  // Load additional order data if needed
})
</script>

<style scoped>
.order-detail {
  max-width: 100%;
}

@media print {
  .order-detail {
    print-color-adjust: exact;
  }
  
  button {
    display: none !important;
  }
}
</style>