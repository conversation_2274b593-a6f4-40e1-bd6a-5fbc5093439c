<template>
  <div class="p-6">
    <div class="flex items-start space-x-4">
      <!-- Artwork Image -->
      <div class="flex-shrink-0">
        <RouterLink :to="`/artwork/${item.artwork.id}`">
          <img 
            :src="item.artwork.imageUrl" 
            :alt="item.artwork.title"
            class="w-24 h-24 object-cover rounded-lg border border-gray-200 hover:opacity-75 transition-opacity duration-200"
            @error="handleImageError"
          />
        </RouterLink>
      </div>

      <!-- Item Details -->
      <div class="flex-1 min-w-0">
        <div class="flex items-start justify-between">
          <div class="flex-1 pr-4">
            <!-- Title and Artist -->
            <RouterLink 
              :to="`/artwork/${item.artwork.id}`"
              class="block hover:text-blue-600 transition-colors duration-200"
            >
              <h3 class="text-lg font-medium text-gray-900 line-clamp-1">
                {{ item.artwork.title }}
              </h3>
              <p class="text-sm text-gray-600 mt-1">by {{ item.artwork.artist }}</p>
            </RouterLink>

            <!-- Artwork Details -->
            <div class="mt-2 space-y-1">
              <p class="text-sm text-gray-500">{{ item.artwork.medium }}</p>
              <p class="text-sm text-gray-500">{{ item.artwork.category }}</p>
              <div class="flex items-center space-x-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {{ item.artwork.style }}
                </span>
              </div>
            </div>

            <!-- Mobile Actions -->
            <div class="mt-4 flex items-center justify-between sm:hidden">
              <div class="flex items-center space-x-3">
                <!-- Quantity Controls -->
                <div class="flex items-center border border-gray-300 rounded-md">
                  <button
                    @click="decreaseQuantity"
                    :disabled="item.quantity <= 1"
                    class="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                    </svg>
                  </button>
                  <span class="px-3 py-1 text-sm font-medium text-gray-900 min-w-[2rem] text-center">
                    {{ item.quantity }}
                  </span>
                  <button
                    @click="increaseQuantity"
                    class="p-1 text-gray-400 hover:text-gray-600"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </button>
                </div>

                <!-- Remove Button -->
                <button
                  @click="removeFromCart"
                  class="text-red-600 hover:text-red-800 text-sm font-medium"
                >
                  Remove
                </button>
              </div>

              <!-- Price -->
              <div class="text-right">
                <p class="text-lg font-bold text-gray-900">
                  ${{ formatPrice(itemTotal) }}
                </p>
                <p v-if="item.quantity > 1" class="text-sm text-gray-500">
                  ${{ formatPrice(item.artwork.price) }} each
                </p>
              </div>
            </div>
          </div>

          <!-- Desktop Price -->
          <div class="hidden sm:block text-right">
            <p class="text-lg font-bold text-gray-900">
              ${{ formatPrice(itemTotal) }}
            </p>
            <p v-if="item.quantity > 1" class="text-sm text-gray-500">
              ${{ formatPrice(item.artwork.price) }} each
            </p>
          </div>
        </div>

        <!-- Desktop Actions -->
        <div class="hidden sm:flex items-center justify-between mt-4">
          <div class="flex items-center space-x-6">
            <!-- Quantity Controls -->
            <div class="flex items-center space-x-3">
              <label class="text-sm font-medium text-gray-700">Quantity:</label>
              <div class="flex items-center border border-gray-300 rounded-md">
                <button
                  @click="decreaseQuantity"
                  :disabled="item.quantity <= 1"
                  class="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                  </svg>
                </button>
                <span class="px-4 py-2 text-sm font-medium text-gray-900 min-w-[3rem] text-center">
                  {{ item.quantity }}
                </span>
                <button
                  @click="increaseQuantity"
                  class="p-2 text-gray-400 hover:text-gray-600"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center space-x-4">
              <button
                @click="moveToWishlist"
                class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                Save for later
              </button>
              
              <button
                @click="removeFromCart"
                class="text-red-600 hover:text-red-800 text-sm font-medium flex items-center"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Remove
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Added Date -->
    <div class="mt-4 text-xs text-gray-400">
      Added {{ formatDate(item.addedAt) }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { RouterLink } from 'vue-router'
import { useWishlistStore } from '../stores/wishlist'

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update-quantity', 'remove-item'])

const wishlistStore = useWishlistStore()

// Computed properties
const itemTotal = computed(() => {
  return props.item.artwork.price * props.item.quantity
})

// Methods
const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US').format(price)
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) {
    return 'today'
  } else if (diffDays < 7) {
    return `${diffDays} days ago`
  } else {
    return date.toLocaleDateString()
  }
}

const handleImageError = (event) => {
  event.target.src = '/images/placeholder-art.jpg'
}

const increaseQuantity = () => {
  emit('update-quantity', props.item.id, props.item.quantity + 1)
}

const decreaseQuantity = () => {
  if (props.item.quantity > 1) {
    emit('update-quantity', props.item.id, props.item.quantity - 1)
  }
}

const removeFromCart = () => {
  emit('remove-item', props.item.id)
}

const moveToWishlist = () => {
  // Add to wishlist first
  wishlistStore.addItem(props.item.artwork)
  // Then remove from cart
  emit('remove-item', props.item.id)
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>