<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Success Header -->
      <div class="text-center mb-8">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
          <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Payment Successful!</h1>
        <p class="text-lg text-gray-600">Thank you for your purchase. Your order has been confirmed.</p>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span class="ml-3 text-gray-600">Loading order details...</span>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Unable to Load Order</h3>
            <p class="mt-1 text-sm text-red-700">{{ error }}</p>
          </div>
        </div>
      </div>

      <!-- Order Details -->
      <div v-else-if="orderData" class="space-y-8">
        <!-- Order Summary Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-medium text-gray-900">Order Summary</h2>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Confirmed
              </span>
            </div>
          </div>
          
          <div class="px-6 py-4">
            <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Order Number</dt>
                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ orderData.orderId }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Order Date</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(orderData.createdAt) }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                <dd class="mt-1 text-sm text-gray-900 flex items-center">
                  <span class="mr-2">{{ getCurrencyIcon(orderData.payCurrency) }}</span>
                  {{ getCurrencyName(orderData.payCurrency) }}
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Transaction ID</dt>
                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ orderData.paymentId }}</dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- Payment Details Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 class="text-lg font-medium text-gray-900">Payment Details</h2>
          </div>
          
          <div class="px-6 py-4">
            <dl class="space-y-4">
              <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Amount Paid (USD)</dt>
                <dd class="text-sm font-medium text-gray-900">${{ formatPrice(orderData.priceAmount) }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Amount Paid ({{ orderData.payCurrency?.toUpperCase() }})</dt>
                <dd class="text-sm font-medium text-gray-900">
                  {{ formatCryptoAmount(orderData.payAmount, orderData.payCurrency) }} {{ orderData.payCurrency?.toUpperCase() }}
                </dd>
              </div>
              <div v-if="orderData.actuallyPaid" class="flex justify-between">
                <dt class="text-sm text-gray-600">Actually Received</dt>
                <dd class="text-sm font-medium text-gray-900">
                  {{ formatCryptoAmount(orderData.actuallyPaid, orderData.payCurrency) }} {{ orderData.payCurrency?.toUpperCase() }}
                </dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Payment Status</dt>
                <dd class="text-sm font-medium text-green-600">Confirmed</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Confirmation Time</dt>
                <dd class="text-sm font-medium text-gray-900">{{ formatDate(orderData.updatedAt) }}</dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- Order Items Card -->
        <div v-if="orderData.orderItems && orderData.orderItems.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 class="text-lg font-medium text-gray-900">Order Items</h2>
          </div>
          
          <div class="divide-y divide-gray-200">
            <div v-for="item in orderData.orderItems" :key="item.id" class="px-6 py-4">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <img 
                    :src="item.artwork.imageUrl" 
                    :alt="item.artwork.title"
                    class="h-16 w-16 rounded-lg object-cover"
                  />
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="text-sm font-medium text-gray-900 truncate">{{ item.artwork.title }}</h3>
                  <p class="text-sm text-gray-500">by {{ item.artwork.artist }}</p>
                  <p class="text-sm text-gray-500">Quantity: {{ item.quantity }}</p>
                </div>
                <div class="flex-shrink-0">
                  <p class="text-sm font-medium text-gray-900">${{ formatPrice(item.artwork.price * item.quantity) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Next Steps Card -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 class="text-lg font-medium text-blue-900 mb-4">What happens next?</h3>
          <div class="space-y-3 text-sm text-blue-800">
            <div class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-200 flex items-center justify-center mr-3 mt-0.5">
                <span class="text-xs font-bold text-blue-900">1</span>
              </div>
              <div>
                <p class="font-medium">Order Processing</p>
                <p>We'll prepare your artwork(s) for shipping and generate certificates of authenticity.</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-200 flex items-center justify-center mr-3 mt-0.5">
                <span class="text-xs font-bold text-blue-900">2</span>
              </div>
              <div>
                <p class="font-medium">Secure Packaging</p>
                <p>Your artwork will be professionally packaged with insurance for safe delivery.</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-200 flex items-center justify-center mr-3 mt-0.5">
                <span class="text-xs font-bold text-blue-900">3</span>
              </div>
              <div>
                <p class="font-medium">Shipping & Tracking</p>
                <p>You'll receive tracking information once your order ships (typically 2-3 business days).</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4">
          <button
            @click="downloadReceipt"
            class="flex-1 inline-flex items-center justify-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Download Receipt
          </button>
          
          <RouterLink
            to="/gallery"
            class="flex-1 inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Continue Shopping
          </RouterLink>
        </div>

        <!-- Support Information -->
        <div class="bg-gray-50 rounded-lg p-6 text-center">
          <h3 class="text-lg font-medium text-gray-900 mb-2">Need Help?</h3>
          <p class="text-sm text-gray-600 mb-4">
            If you have any questions about your order, please don't hesitate to contact us.
          </p>
          <div class="space-y-2 text-sm text-gray-600">
            <p>Email: <EMAIL></p>
            <p>Phone: +****************</p>
            <p>Hours: Monday - Friday, 9 AM - 6 PM EST</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, RouterLink } from 'vue-router'
import { usePaymentStore } from '../stores/payment'
import paymentService from '../services/paymentService'

// Route
const route = useRoute()

// Store
const paymentStore = usePaymentStore()

// Local state
const loading = ref(true)
const error = ref(null)
const orderData = ref(null)

// Props
const props = defineProps({
  orderId: {
    type: String,
    default: null
  }
})

// Computed
const currentOrderId = computed(() => props.orderId || route.params.orderId)

// Methods
const loadOrderData = async () => {
  loading.value = true
  error.value = null
  
  try {
    // First try to get from current payment
    if (paymentStore.currentPayment && paymentStore.currentPayment.orderId === currentOrderId.value) {
      orderData.value = paymentStore.currentPayment
    } else {
      // Try to find in payment history
      const historicalPayment = paymentStore.getPaymentById(currentOrderId.value)
      if (historicalPayment) {
        orderData.value = historicalPayment
      } else {
        throw new Error('Order not found')
      }
    }
  } catch (err) {
    error.value = err.message || 'Failed to load order details'
    console.error('Error loading order:', err)
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US').format(price)
}

const formatCryptoAmount = (amount, currency) => {
  return paymentService.formatCryptoAmount(amount, currency)
}

const getCurrencyIcon = (currency) => {
  const icons = {
    'btc': '₿',
    'eth': 'Ξ',
    'ltc': 'Ł',
    'usdt': '₮',
    'usdc': '$'
  }
  return icons[currency?.toLowerCase()] || '₿'
}

const getCurrencyName = (currency) => {
  const names = {
    'btc': 'Bitcoin',
    'eth': 'Ethereum',
    'ltc': 'Litecoin',
    'usdt': 'Tether USD',
    'usdc': 'USD Coin'
  }
  return names[currency?.toLowerCase()] || currency?.toUpperCase()
}

const downloadReceipt = () => {
  if (!orderData.value) return
  
  // Create receipt content
  const receiptContent = `
CONTEMPORARY ART STORE
Payment Receipt

Order Number: ${orderData.value.orderId}
Transaction ID: ${orderData.value.paymentId}
Date: ${formatDate(orderData.value.createdAt)}

PAYMENT DETAILS
Amount (USD): $${formatPrice(orderData.value.priceAmount)}
Amount (${orderData.value.payCurrency?.toUpperCase()}): ${formatCryptoAmount(orderData.value.payAmount, orderData.value.payCurrency)} ${orderData.value.payCurrency?.toUpperCase()}
Payment Method: ${getCurrencyName(orderData.value.payCurrency)}
Status: Confirmed

${orderData.value.orderItems ? `
ORDER ITEMS
${orderData.value.orderItems.map(item => 
  `${item.artwork.title} by ${item.artwork.artist} - Qty: ${item.quantity} - $${formatPrice(item.artwork.price * item.quantity)}`
).join('\n')}
` : ''}

Thank you for your purchase!
For support, contact: <EMAIL>
  `.trim()
  
  // Create and download file
  const blob = new Blob([receiptContent], { type: 'text/plain' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `receipt-${orderData.value.orderId}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

// Lifecycle
onMounted(() => {
  if (currentOrderId.value) {
    loadOrderData()
  } else {
    error.value = 'No order ID provided'
    loading.value = false
  }
})
</script>