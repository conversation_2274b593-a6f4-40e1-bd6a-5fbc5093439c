<template>
  <div class="relative">
    <div class="relative">
      <input
        v-model="searchQuery"
        type="text"
        :placeholder="placeholder"
        class="w-full pl-12 pr-12 py-3 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-sm"
        @input="handleSearch"
        @focus="showSuggestions = true"
        @blur="hideSuggestions"
        @keydown="handleKeydown"
        ref="searchInput"
      />
      
      <!-- Search Icon -->
      <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      
      <!-- Clear Button -->
      <button
        v-if="searchQuery"
        @click="clearSearch"
        class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600"
      >
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Search Suggestions Dropdown -->
    <div
      v-if="showSuggestions && (searchSuggestions.length > 0 || recentSearches.length > 0)"
      class="absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto"
    >
      <!-- Search Suggestions -->
      <div v-if="searchSuggestions.length > 0">
        <div class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
          Suggestions
        </div>
        <button
          v-for="(suggestion, index) in searchSuggestions"
          :key="`suggestion-${index}`"
          @mousedown="selectSuggestion(suggestion)"
          class="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-50 last:border-b-0"
          :class="{ 'bg-gray-50': index === selectedSuggestionIndex }"
        >
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium text-gray-900">{{ suggestion.title }}</div>
              <div class="text-xs text-gray-500">{{ suggestion.type }}</div>
            </div>
          </div>
        </button>
      </div>

      <!-- Recent Searches -->
      <div v-if="recentSearches.length > 0 && !searchQuery">
        <div class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
          Recent Searches
        </div>
        <button
          v-for="(recent, index) in recentSearches"
          :key="`recent-${index}`"
          @mousedown="selectRecentSearch(recent)"
          class="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-50 last:border-b-0 group"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="text-sm text-gray-700">{{ recent }}</div>
            </div>
            <button
              @click.stop="removeRecentSearch(recent)"
              class="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-gray-600"
            >
              <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </button>
      </div>

      <!-- No Results -->
      <div v-if="searchQuery && searchSuggestions.length === 0" class="px-4 py-6 text-center text-gray-500">
        <svg class="mx-auto h-8 w-8 text-gray-300 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
        <p class="text-sm">No suggestions found for "{{ searchQuery }}"</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useArtworkStore } from '../stores/artwork'

const props = defineProps({
  placeholder: {
    type: String,
    default: 'Search artworks, artists, styles...'
  },
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'search'])

const artworkStore = useArtworkStore()
const searchInput = ref(null)
const searchQuery = ref(props.modelValue)
const showSuggestions = ref(false)
const selectedSuggestionIndex = ref(-1)
const recentSearches = ref([])

// Load recent searches from localStorage
onMounted(() => {
  try {
    const saved = localStorage.getItem('artstore_recent_searches')
    if (saved) {
      recentSearches.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('Failed to load recent searches:', error)
  }
})

// Generate search suggestions based on current query
const searchSuggestions = computed(() => {
  if (!searchQuery.value || searchQuery.value.length < 2) {
    return []
  }

  const query = searchQuery.value.toLowerCase()
  const suggestions = []
  const seen = new Set()

  // Search in artwork titles
  artworkStore.artworks.forEach(artwork => {
    if (artwork.title.toLowerCase().includes(query) && !seen.has(artwork.title)) {
      suggestions.push({
        title: artwork.title,
        type: 'Artwork',
        value: artwork.title,
        artwork
      })
      seen.add(artwork.title)
    }
  })

  // Search in artist names
  artworkStore.artworks.forEach(artwork => {
    if (artwork.artist.toLowerCase().includes(query) && !seen.has(artwork.artist)) {
      suggestions.push({
        title: artwork.artist,
        type: 'Artist',
        value: artwork.artist,
        artwork
      })
      seen.add(artwork.artist)
    }
  })

  // Search in categories
  artworkStore.artworks.forEach(artwork => {
    if (artwork.category.toLowerCase().includes(query) && !seen.has(artwork.category)) {
      suggestions.push({
        title: artwork.category,
        type: 'Category',
        value: artwork.category,
        artwork
      })
      seen.add(artwork.category)
    }
  })

  // Search in styles
  artworkStore.artworks.forEach(artwork => {
    if (artwork.style.toLowerCase().includes(query) && !seen.has(artwork.style)) {
      suggestions.push({
        title: artwork.style,
        type: 'Style',
        value: artwork.style,
        artwork
      })
      seen.add(artwork.style)
    }
  })

  return suggestions.slice(0, 8) // Limit to 8 suggestions
})

const handleSearch = () => {
  emit('update:modelValue', searchQuery.value)
  emit('search', searchQuery.value)
  selectedSuggestionIndex.value = -1
}

const clearSearch = () => {
  searchQuery.value = ''
  handleSearch()
  searchInput.value?.focus()
}

const selectSuggestion = (suggestion) => {
  searchQuery.value = suggestion.value
  showSuggestions.value = false
  addToRecentSearches(suggestion.value)
  handleSearch()
}

const selectRecentSearch = (recent) => {
  searchQuery.value = recent
  showSuggestions.value = false
  handleSearch()
}

const addToRecentSearches = (query) => {
  if (!query.trim()) return
  
  // Remove if already exists
  const index = recentSearches.value.indexOf(query)
  if (index > -1) {
    recentSearches.value.splice(index, 1)
  }
  
  // Add to beginning
  recentSearches.value.unshift(query)
  
  // Keep only last 10 searches
  recentSearches.value = recentSearches.value.slice(0, 10)
  
  // Save to localStorage
  try {
    localStorage.setItem('artstore_recent_searches', JSON.stringify(recentSearches.value))
  } catch (error) {
    console.error('Failed to save recent searches:', error)
  }
}

const removeRecentSearch = (query) => {
  const index = recentSearches.value.indexOf(query)
  if (index > -1) {
    recentSearches.value.splice(index, 1)
    try {
      localStorage.setItem('artstore_recent_searches', JSON.stringify(recentSearches.value))
    } catch (error) {
      console.error('Failed to save recent searches:', error)
    }
  }
}

const hideSuggestions = () => {
  // Delay hiding to allow click events on suggestions
  setTimeout(() => {
    showSuggestions.value = false
  }, 200)
}

const handleKeydown = (event) => {
  if (!showSuggestions.value) return

  const suggestions = searchSuggestions.value
  
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedSuggestionIndex.value = Math.min(selectedSuggestionIndex.value + 1, suggestions.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedSuggestionIndex.value = Math.max(selectedSuggestionIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (selectedSuggestionIndex.value >= 0 && suggestions[selectedSuggestionIndex.value]) {
        selectSuggestion(suggestions[selectedSuggestionIndex.value])
      } else {
        showSuggestions.value = false
        addToRecentSearches(searchQuery.value)
        handleSearch()
      }
      break
    case 'Escape':
      showSuggestions.value = false
      searchInput.value?.blur()
      break
  }
}

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  searchQuery.value = newValue
})
</script>