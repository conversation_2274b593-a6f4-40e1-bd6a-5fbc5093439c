# Contemporary Art Store

A modern, production-ready Vue.js application for a contemporary art store featuring both customer-facing gallery and comprehensive admin dashboard functionality. Built with Vue 3, Vite, and Tailwind CSS, optimized for static hosting deployment.

## 🎨 Features

### Customer Features
- **Art Gallery** - Browse curated contemporary artworks with advanced filtering
- **Product Details** - Detailed artwork information with high-quality images
- **Shopping Cart** - Add/remove items with persistent cart state
- **Cryptocurrency Payments** - Secure payments via NOWPayments integration
- **User Authentication** - Registration, login, and profile management
- **Order History** - Track purchase history and order status
- **Wishlist** - Save favorite artworks for later
- **Responsive Design** - Optimized for all devices

### Admin Features
- **Dashboard Analytics** - Sales metrics, user statistics, and performance insights
- **Artwork Management** - Full CRUD operations for art catalog
- **Order Management** - Process orders, update status, and track payments
- **User Management** - Manage customer accounts and roles
- **Inventory Control** - Track stock levels and availability
- **Role-Based Access** - Admin and curator role permissions
- **System Settings** - Configure application settings

### Technical Features
- **Vue 3 with Composition API** - Modern Vue.js development
- **Vite** - Lightning-fast development and optimized production builds
- **Tailwind CSS** - Utility-first CSS framework for responsive design
- **Vue Router** - Client-side routing with lazy loading
- **Pinia** - Modern state management
- **TypeScript Support** - Type-safe development
- **ESLint & Prettier** - Code quality and formatting
- **Production Optimized** - Code splitting, minification, and caching
- **Static Hosting Ready** - Configured for Netlify, Vercel, GitHub Pages

## 📁 Project Structure

```
contemporary-art-store/
├── src/
│   ├── components/           # Reusable Vue components
│   │   ├── ArtworkCard.vue   # Gallery artwork display
│   │   ├── CartItem.vue      # Shopping cart components
│   │   ├── AdminLayout.vue   # Admin dashboard layout
│   │   └── ...               # Other UI components
│   ├── views/               # Page components
│   │   ├── Home.vue         # Landing page
│   │   ├── Gallery.vue      # Art gallery
│   │   ├── ProductDetail.vue # Artwork details
│   │   ├── Cart.vue         # Shopping cart
│   │   ├── Admin.vue        # Admin dashboard
│   │   ├── Login.vue        # Authentication
│   │   └── ...              # Other pages
│   ├── stores/              # Pinia state management
│   │   ├── auth.js          # Authentication store
│   │   ├── artwork.js       # Artwork catalog store
│   │   ├── cart.js          # Shopping cart store
│   │   ├── payment.js       # Payment processing store
│   │   ├── wishlist.js      # Wishlist store
│   │   └── admin.js         # Admin functionality store
│   ├── services/            # API and mock data services
│   │   ├── artworkService.js # Artwork CRUD operations
│   │   ├── authService.js   # Authentication service
│   │   ├── paymentService.js # NOWPayments integration
│   │   └── mockData.js      # Mock database data
│   ├── router/              # Vue Router configuration
│   │   └── index.js         # Route definitions with guards
│   ├── types/               # TypeScript type definitions
│   ├── utils/               # Helper functions
│   ├── assets/              # Static assets and CSS
│   └── main.js              # Application entry point
├── public/                  # Public static files
│   ├── _redirects           # Netlify SPA routing
│   └── favicon.ico          # Application icon
├── dist/                    # Production build output
├── docs/                    # Documentation
├── .github/                 # GitHub Actions workflows
├── netlify.toml             # Netlify configuration
├── vercel.json              # Vercel configuration
├── vite.config.js           # Vite build configuration
├── DEPLOYMENT.md            # Deployment guide
└── README.md                # This file
```

## 🚀 Getting Started

### Prerequisites

- **Node.js** (version 18 or higher)
- **npm** or **yarn** package manager
- **Git** for version control
- **NOWPayments API key** (for payment functionality)

### Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd contemporary-art-store
```

2. **Install dependencies**:
```bash
npm install
```

3. **Environment Setup**:
```bash
# Copy environment template
cp src/.env.example .env.local

# Edit .env.local with your configuration
# Add your NOWPayments API key and other settings
```

4. **Start the development server**:
```bash
npm run dev
```

5. **Open your browser** and navigate to `http://localhost:3000`

### Environment Configuration

Create a `.env.local` file with the following variables:

```env
# NOWPayments Configuration
VITE_NOWPAYMENTS_API_KEY=your_api_key_here
VITE_NOWPAYMENTS_SANDBOX=true

# Application Configuration
VITE_APP_NAME=Contemporary Art Store
VITE_APP_URL=http://localhost:5173

# Feature Flags
VITE_ENABLE_REGISTRATION=true
VITE_ENABLE_GUEST_CHECKOUT=true
VITE_ENABLE_WISHLIST=true

# Admin Configuration
VITE_ADMIN_EMAIL=<EMAIL>
```

### Available Scripts

- `npm run dev` - Start development server (port 3000)
- `npm run build` - Build for production with optimizations
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint for code quality
- `npm run format` - Format code with Prettier

### Development Workflow

1. **Development**: Use `npm run dev` for hot-reload development
2. **Code Quality**: Run `npm run lint` and `npm run format` before commits
3. **Testing**: Use `npm run preview` to test production builds locally
4. **Building**: Use `npm run build` to create optimized production builds

## 🔐 Authentication & User Roles

### Test Accounts

The application includes role-based access control with the following test accounts:

- **Admin**: <EMAIL> / admin123
  - Full system access, user management, analytics
- **Curator**: <EMAIL> / curator123
  - Artwork management, order processing
- **Customer**: <EMAIL> / customer123
  - Shopping, order history, profile management

### User Roles & Permissions

- **Admin**: Complete system control, user management, settings
- **Curator**: Artwork and order management, limited admin access
- **Customer**: Shopping, profile, and order management

## 🎨 Art Collection

The application features a curated collection of contemporary artworks from renowned artists including:

- Andy Warhol - Campbell's Soup Cans
- Jackson Pollock - Autumn Rhythm
- Louise Bourgeois - Maman
- David Hockney - Pool with Two Figures
- Banksy - Girl with Balloon
- Yayoi Kusama - Infinity Rooms
- Jean-Michel Basquiat - Untitled works
- And many more contemporary masterpieces...

## 🛠 Technology Stack

### Frontend
- **Vue 3** - Progressive JavaScript framework with Composition API
- **Vite** - Next-generation frontend build tool
- **Tailwind CSS** - Utility-first CSS framework
- **Vue Router 4** - Official router with lazy loading
- **Pinia** - Modern state management for Vue

### Development Tools
- **TypeScript** - Type-safe JavaScript development
- **ESLint** - Code linting and quality assurance
- **Prettier** - Code formatting
- **PostCSS** - CSS processing and optimization

### Payment Integration
- **NOWPayments** - Cryptocurrency payment processing
- **QR Code Vue3** - QR code generation for payments

### Build & Deployment
- **Vite Build** - Optimized production builds
- **Code Splitting** - Automatic chunk splitting for performance
- **Static Hosting** - Ready for Netlify, Vercel, GitHub Pages

## 🏗 Architecture

### State Management (Pinia Stores)
- **Auth Store** - User authentication and role management
- **Artwork Store** - Art catalog and inventory management
- **Cart Store** - Shopping cart functionality
- **Payment Store** - Payment processing and order management
- **Wishlist Store** - User wishlist functionality
- **Admin Store** - Administrative operations and analytics

### Services Layer
- **Artwork Service** - CRUD operations for artworks
- **Auth Service** - Authentication and user management
- **Payment Service** - NOWPayments integration
- **Mock Data Service** - Simulated backend functionality

### Component Architecture
- **Reusable Components** - Modular, maintainable UI components
- **View Components** - Page-level components with routing
- **Layout Components** - Consistent application layouts
- **Form Components** - Reusable form elements and validation

## 🚀 Deployment

### Quick Deploy

The application is optimized for static hosting platforms:

#### Netlify
```bash
# Automatic deployment via Git integration
# Or manual deployment:
npm run build
netlify deploy --prod --dir=dist
```

#### Vercel
```bash
# Automatic deployment via Git integration
# Or using Vercel CLI:
npm run build
vercel --prod
```

#### GitHub Pages
```bash
# Copy github-pages-deploy.yml to .github/workflows/
# Enable GitHub Pages in repository settings
```

### Configuration Files Included
- `netlify.toml` - Netlify configuration with redirects and headers
- `vercel.json` - Vercel deployment configuration
- `public/_redirects` - SPA routing for static hosts
- `github-pages-deploy.yml` - GitHub Actions workflow

For detailed deployment instructions, see [DEPLOYMENT.md](./DEPLOYMENT.md).

## 📊 Performance Optimizations

### Build Optimizations
- **Code Splitting** - Automatic route-based code splitting
- **Tree Shaking** - Eliminate unused code
- **Minification** - JavaScript and CSS minification
- **Asset Optimization** - Image and static asset optimization
- **Caching** - Optimized caching strategies for static assets

### Bundle Analysis
- **Chunk Splitting** - Vendor, UI, and feature-based chunks
- **Lazy Loading** - All routes and components are lazy-loaded
- **Modern Build Target** - ESNext for modern browsers
- **Compressed Assets** - Gzip compression support

## 🔧 Development

### Mock Services
The application uses comprehensive mock services to simulate backend functionality:

- **artworkService.js** - Complete artwork CRUD operations
- **authService.js** - User authentication and role management
- **paymentService.js** - Payment processing simulation
- **mockData.js** - Realistic contemporary art data

### Development Features
- **Hot Module Replacement** - Instant updates during development
- **TypeScript Support** - Type checking and IntelliSense
- **ESLint Integration** - Real-time code quality feedback
- **Prettier Formatting** - Consistent code formatting

## 📝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes and test thoroughly
4. Run linting and formatting: `npm run lint && npm run format`
5. Commit your changes: `git commit -m 'Add new feature'`
6. Push to the branch: `git push origin feature/new-feature`
7. Submit a pull request

## 📄 License

This project is licensed under the ISC License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

1. Check the [DEPLOYMENT.md](./DEPLOYMENT.md) guide for deployment issues
2. Review the documentation in the `docs/` directory
3. Check existing issues in the repository
4. Create a new issue with detailed information

## 🔗 Links

- [Vue.js Documentation](https://vuejs.org/)
- [Vite Documentation](https://vitejs.dev/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [NOWPayments API Documentation](https://documenter.getpostman.com/view/7907941/S1a32n38)
- [Deployment Guide](./DEPLOYMENT.md)