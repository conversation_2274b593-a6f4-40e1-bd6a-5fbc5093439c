import js from '@eslint/js'
import pluginVue from 'eslint-plugin-vue'
import * as parserVue from 'vue-eslint-parser'
import configPrettier from 'eslint-config-prettier'
import pluginPrettier from 'eslint-plugin-prettier'
import { defineFlatConfig } from 'eslint-define-config'
import * as parserTypeScript from '@typescript-eslint/parser'
import pluginTypeScript from '@typescript-eslint/eslint-plugin'

export default defineFlatConfig([
  {
    ...js.configs.recommended,
    ignores: ['dist', 'node_modules']
  },
  ...pluginVue.configs['flat/recommended'],
  {
    plugins: {
      '@typescript-eslint': pluginTypeScript,
      prettier: pluginPrettier
    },
    languageOptions: {
      parser: parserVue,
      parserOptions: {
        parser: parserTypeScript,
        ecmaVersion: 'latest',
        sourceType: 'module'
      }
    },
    rules: {
      ...configPrettier.rules,
      'prettier/prettier': 'error',
      'vue/require-default-prop': 'off',
      'vue/multi-word-component-names': 'off',
      '@typescript-eslint/no-unused-vars': 'error'
    }
  }
])